# =====================================
# 🤖 Caby AI 人脸检测服务配置示例
# =====================================

# === 核心服务鉴权 ===
# 服务间通信token，必须设置
CABY_AI_SERVICE_TOKEN=your_caby_ai_service_token_here

# === LitServe 人脸检测服务配置 ===
VISION_HOST=0.0.0.0
VISION_PORT=8001
VISION_API_KEY=your_vision_api_key_here

# === Caby AI 主服务配置 ===
CABY_AI_PORT=8765
CABY_AI_HOST=0.0.0.0

# === Qdrant 向量数据库配置 ===
# Qdrant API密钥，用于向量存储认证
QDRANT_API_KEY=your_qdrant_api_key_here
QDRANT_HOST=qdrant
QDRANT_PORT=6333

# === 应用配置 ===
GIN_MODE=release
LOG_LEVEL=INFO

# === 代理设置（构建时使用，如需要请取消注释）===
# 如果你在防火墙后面需要代理来下载依赖，请设置以下环境变量
# http_proxy=http://127.0.0.1:10808
# https_proxy=http://127.0.0.1:10808
# HTTP_PROXY=http://127.0.0.1:10808
# HTTPS_PROXY=http://127.0.0.1:10808

# === Docker构建配置 ===
# 数据持久化目录
QDRANT_DATA_DIR=./data/qdrant
MODELS_DIR=./models

# === 安全配置（可选）===
# CORS跨域设置
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
# 信任的代理
TRUSTED_PROXIES=127.0.0.1

# =====================================
# 配置说明：
# 1. CABY_AI_SERVICE_TOKEN: 必须设置，用于API鉴权
# 2. QDRANT_API_KEY: 必须设置，用于向量数据库认证
# 3. VISION_API_KEY: Vision服务API密钥
# 4. 模型文件路径在Docker容器内已配置，通常无需修改
# =====================================