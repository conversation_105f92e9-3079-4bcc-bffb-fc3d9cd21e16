# Caby AI API 文档

## 基本信息

- **基础URL**: `http://your-server.com:8765`
- **认证方式**: Bearer Token
- **数据格式**: JSON
- **编码**: UTF-8

## 认证

所有API请求都需要在请求头中包含认证信息：

```
Authorization: Bearer <your-api-key>
```

## 健康检查

### 1. 服务健康检查
```
GET /health
```

**说明**: 检查服务运行状态

**响应:**
```json
{
  "status": "ok",
  "timestamp": "2025-01-18T10:00:00Z"
}
```

### 2. 视觉服务健康检查
```
GET /vision/health
```

**说明**: 检查视觉服务连接状态

**响应:**
```json
{
  "status": "ok",
  "vision_service": "connected",
  "timestamp": "2025-01-18T10:00:00Z"
}
```

## 视频分析

### 1. 视频分析
```
POST /api/v1/analyze
```

**说明**: 分析视频并识别猫咪，支持状态感知的特征保护

**请求头:**
```
Authorization: Bearer <token>
X-User-ID: <user-id>
Content-Type: application/json
```

**请求体:**
```json
{
  "video_id": "string",
  "device_id": "string", 
  "user_id": "string",
  "video_path": "string",
  "thumbnail_base64": "string (optional)",
  "cat_states": [
    {
      "cat_id": "string",
      "state": "medical|clothing|grooming|other",
      "description": "string",
      "start_time": "2025-01-18T10:00:00Z",
      "end_time": "2025-01-25T10:00:00Z (optional)"
    }
  ]
}
```

**响应:**
```json
{
  "video_id": "string",
  "analysis_result": {
    "cat_detected": true,
    "cat_id": "string",
    "confidence": 0.95,
    "behavior_type": "normal|abnormal",
    "abnormal_details": {
      "type": "string",
      "probability": 0.8
    }
  },
  "shadow_result": {
    "cat_id": "string",
    "similarity": 0.92,
    "confidence": 0.88,
    "model_version": "v2.1",
    "is_new_cat": false,
    "feature_protected": true,
    "protection_reason": "medical"
  },
  "timestamp": "2025-01-18T10:00:00Z"
}
```

## 相似度搜索

### 1. 图像相似度搜索
```
POST /api/v1/search
```

**说明**: 基于图像搜索相似的猫咪特征

**请求头:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体:**
```json
{
  "user_id": "string",
  "image_base64": "string",
  "limit": 10,
  "threshold": 0.7
}
```

**响应:**
```json
{
  "results": [
    {
      "cat_id": "string",
      "similarity": 0.95,
      "confidence": 0.88,
      "timestamp": "2025-01-18T10:00:00Z",
      "metadata": {
        "behavior_type": "string",
        "is_protected": false
      }
    }
  ],
  "total": 5,
  "query_time": "150ms"
}
```

## 影子模式管理

### 1. 获取影子模式配置
```
GET /api/v1/shadow/config
```

**说明**: 获取当前影子模式配置

**请求头:**
```
Authorization: Bearer <token>
```

**响应:**
```json
{
  "enabled": true,
  "similarity_threshold": 0.85,
  "new_cat_threshold": 0.70,
  "top_k": 5,
  "store_features": true,
  "max_features_per_cat": 300,
  "evolution_strategy": "hybrid",
  "evolution_cycle": 336,
  "feature_protection": true,
  "recent_days_protection": 7
}
```

### 2. 更新影子模式配置
```
PUT /api/v1/shadow/config
```

**说明**: 更新影子模式配置

**请求头:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体:**
```json
{
  "similarity_threshold": 0.85,
  "new_cat_threshold": 0.70,
  "max_features_per_cat": 300,
  "feature_protection": true
}
```

**响应:**
```json
{
  "success": true,
  "message": "Configuration updated successfully"
}
```

## 管理接口

### 1. 初始化猫咪数据
```
POST /api/admin/init-cats
```

**说明**: 批量初始化猫咪特征数据

**请求头:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体:**
```json
{
  "cats": [
    {
      "cat_id": "string",
      "user_id": "string",
      "features_count": 10
    }
  ]
}
```

**响应:**
```json
{
  "success": true,
  "initialized_cats": 5,
  "total_features": 50
}
```

### 2. 清理测试数据
```
DELETE /api/admin/cleanup
```

**说明**: 清理测试数据

**请求头:**
```
Authorization: Bearer <token>
```

**响应:**
```json
{
  "success": true,
  "deleted_features": 100,
  "deleted_collections": 2
}
```

## 错误响应

所有API在出错时都会返回统一的错误格式：

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": "Additional error details"
  },
  "timestamp": "2025-01-18T10:00:00Z"
}
```

### 常见错误码

- `UNAUTHORIZED`: 认证失败
- `INVALID_REQUEST`: 请求参数无效
- `VISION_SERVICE_ERROR`: 视觉服务错误
- `QDRANT_ERROR`: 向量数据库错误
- `INTERNAL_ERROR`: 内部服务器错误

## 状态码

- `200`: 成功
- `400`: 请求参数错误
- `401`: 认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 内部服务器错误
- `503`: 服务不可用

## 限制说明

- 图像大小限制: 最大10MB
- 请求频率限制: 每分钟100次
- 批量操作限制: 每次最多处理50个项目
- 特征存储限制: 每只猫最多300个特征向量

## 版本信息

- **当前版本**: v1.0
- **API版本**: v1
- **最后更新**: 2025-01-18
