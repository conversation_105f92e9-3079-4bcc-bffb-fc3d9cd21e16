package auth

import (
	"net/http"
	"strings"

	"caby-ai/config"

	"github.com/gin-gonic/gin"
)

const (
	AuthHeader = "Authorization"
	BearerPrefix = "Bearer "
)

func ServiceTokenAuth(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>(AuthHeader)
		if authHeader == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			return
		}

		if !strings.HasPrefix(authHeader, BearerPrefix) {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization format"})
			return
		}

		token := strings.TrimPrefix(authHeader, BearerPrefix)
		if token != cfg.Auth.ServiceToken {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid service token"})
			return
		}

		c.Next()
	}
}
