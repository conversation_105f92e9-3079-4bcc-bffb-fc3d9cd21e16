package models

import "time"

// RecordShit represents the input data for an analysis request.
// Based on backend_server/pkg/cattoilet/model.go
type RecordShit struct {
	VideoID      string    `json:"video_id"`
	DeviceID     string    `json:"device_id"`
	UserID       string    `json:"user_id,omitempty"`       // UserID provided by backend_server
	KnownCatIDs  []string  `json:"known_cat_ids,omitempty"` // Known cat IDs for the user
	StartTime    string    `json:"start_time"`              // ISO8601 formatted time string
	EndTime      string    `json:"end_time,omitempty"`      // ISO8601 formatted time string
	Status       int8      `json:"status,omitempty"`
	ProcessStage int8      `json:"process_stage,omitempty"`
	WeightLitter float64   `json:"weight_litter"`
	WeightCat    float64   `json:"weight_cat"`
	WeightWaste  float64   `json:"weight_waste"`
	VideoPath    string    `json:"video_path"` // Assuming video path is provided or derivable
	FileSize     int64     `json:"file_size,omitempty"`
	Type         string    `json:"type,omitempty"` // e.g., "shit", "pee"
	CreatedAt    time.Time `json:"created_at,omitempty"`
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	// 猫咪状态信息（用于特征保护）
	CatStates []CatStateInfo `json:"cat_states,omitempty"` // 当前活跃的猫咪状态
}

// CatStateInfo 猫咪状态信息
type CatStateInfo struct {
	CatID       string `json:"cat_id"`
	State       string `json:"state"` // "medical", "clothing", "grooming", "other"
	Description string `json:"description"`
	StartTime   string `json:"start_time"` // ISO8601 formatted time string
	EndTime     string `json:"end_time"`   // ISO8601 formatted time string (expected)
}

// RecordAnalysis represents the output data from an analysis request.
// Based on backend_server/pkg/cattoilet/model.go
type RecordAnalysis struct {
	VideoID       string    `json:"video_id"`
	AnimalID      string    `json:"animal_id"`      // Identified CatID or "unknown"
	CatConfidence float64   `json:"cat_confidence"` // Confidence score for cat identification
	BehaviorType  string    `json:"behavior_type"`  // e.g., "normal_poop", "struggling"
	IsAbnormal    bool      `json:"is_abnormal"`
	AbnormalType  string    `json:"abnormal_type"` // Type of abnormality detected
	AbnormalProb  float64   `json:"abnormal_prob"` // Probability of the abnormality
	AiResults     string    `json:"ai_results"`    // Raw AI model output (JSON string or similar)
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}
