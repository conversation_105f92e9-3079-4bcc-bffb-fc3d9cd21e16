package vision

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// Client Vision HTTP客户端
type Client struct {
	baseURL    string
	apiKey     string
	httpClient *http.Client
}

// PredictionRequest 预测请求结构
type PredictionRequest struct {
	Input interface{} `json:"input,omitempty"`
	Text  string      `json:"text,omitempty"`
	Data  interface{} `json:"data,omitempty"`
}

// PredictionResponse 预测响应结构
type PredictionResponse struct {
	Success bool        `json:"success"`
	Result  interface{} `json:"result"`
	Message string      `json:"message"`
	Error   string      `json:"error,omitempty"`
}

// HealthResponse 健康检查响应
type HealthResponse struct {
	Status string `json:"status"`
}

// NewClient 创建新的Vision客户端
func NewClient(baseURL, apiKey string) *Client {
	// 创建一个自定义的Transport，禁用代理以避免容器内部代理问题
	transport := &http.Transport{
		Proxy: http.ProxyFromEnvironment, // 使用环境变量的代理设置
	}

	// 对于内部服务通信，禁用代理
	if isInternalService(baseURL) {
		transport.Proxy = nil // 禁用代理
	}

	return &Client{
		baseURL: baseURL,
		apiKey:  apiKey,
		httpClient: &http.Client{
			Timeout:   30 * time.Second,
			Transport: transport,
		},
	}
}

// SetTimeout 设置HTTP客户端超时时间
func (c *Client) SetTimeout(timeout time.Duration) {
	c.httpClient.Timeout = timeout
}

// makeRequest 发送HTTP请求的通用方法
func (c *Client) makeRequest(method, endpoint string, payload interface{}) ([]byte, error) {
	url := fmt.Sprintf("%s%s", c.baseURL, endpoint)

	var body io.Reader
	if payload != nil {
		jsonData, err := json.Marshal(payload)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request payload: %w", err)
		}
		body = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	if c.apiKey != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.apiKey))
	}

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return nil, fmt.Errorf("HTTP error %d: %s", resp.StatusCode, string(responseBody))
	}

	return responseBody, nil
}

// Predict 发送预测请求
func (c *Client) Predict(request *PredictionRequest) (*PredictionResponse, error) {
	responseBody, err := c.makeRequest("POST", "/predict", request)
	if err != nil {
		return nil, fmt.Errorf("prediction request failed: %w", err)
	}

	var response PredictionResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("failed to parse prediction response: %w", err)
	}

	return &response, nil
}

// PredictWithText 使用文本输入进行预测的便捷方法
func (c *Client) PredictWithText(text string) (*PredictionResponse, error) {
	request := &PredictionRequest{
		Text: text,
	}
	return c.Predict(request)
}

// PredictWithData 使用任意数据进行预测的便捷方法
func (c *Client) PredictWithData(data interface{}) (*PredictionResponse, error) {
	request := &PredictionRequest{
		Data: data,
	}
	return c.Predict(request)
}

// HealthCheck 检查Vision服务健康状态
func (c *Client) HealthCheck() (*HealthResponse, error) {
	responseBody, err := c.makeRequest("GET", "/health", nil)
	if err != nil {
		return nil, fmt.Errorf("health check failed: %w", err)
	}

	// 尝试解析为JSON响应
	var response HealthResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		// 如果JSON解析失败，尝试处理纯文本响应
		textResponse := strings.TrimSpace(string(responseBody))
		if textResponse == "ok" || textResponse == "healthy" {
			response.Status = textResponse
		} else {
			return nil, fmt.Errorf("failed to parse health response: %w", err)
		}
	}

	return &response, nil
}

// IsHealthy 检查服务是否健康的便捷方法
func (c *Client) IsHealthy() bool {
	health, err := c.HealthCheck()
	if err != nil {
		return false
	}
	return health.Status == "ok" || health.Status == "healthy"
}

// isInternalService 检查URL是否为内部服务（Docker容器间通信）
func isInternalService(baseURL string) bool {
	// 解析URL
	u, err := url.Parse(baseURL)
	if err != nil {
		return false
	}

	// 检查是否为内部Docker服务名称或本地地址
	hostname := strings.ToLower(u.Hostname())
	return hostname == "caby_vision" ||
		hostname == "localhost" ||
		hostname == "127.0.0.1" ||
		strings.HasSuffix(hostname, ".local") ||
		!strings.Contains(hostname, ".")
}
