package shadow

import (
	"context"
	"log"
	"math"
	"sort"
	"time"

	"caby-ai/config"
	"caby-ai/pkg/qdrant"
)

// SimpleProtector 简单特征保护器（立即实施版本）
type SimpleProtector struct {
	config               *config.ShadowModeConfig
	qdrantClient         *qdrant.QdrantClient
	userFeedbackDetector *UserFeedbackDetector
}

// NewSimpleProtector 创建简单特征保护器
func NewSimpleProtector(cfg *config.ShadowModeConfig, qdrantClient *qdrant.QdrantClient, userFeedbackDetector *UserFeedbackDetector) *SimpleProtector {
	return &SimpleProtector{
		config:               cfg,
		qdrantClient:         qdrantClient,
		userFeedbackDetector: userFeedbackDetector,
	}
}

// SimpleFeature 简化的特征结构
type SimpleFeature struct {
	ID           string    `json:"id"`
	CatID        string    `json:"cat_id"`
	UserID       string    `json:"user_id"`
	Similarity   float64   `json:"similarity"`
	Confidence   float64   `json:"confidence"`
	Timestamp    time.Time `json:"timestamp"`
	IsReference  bool      `json:"is_reference"`
	QualityScore float64   `json:"quality_score"`
}

// ProtectionConfig 保护配置
type ProtectionConfig struct {
	Enabled              bool `json:"enabled"`
	RecentDaysProtection int  `json:"recent_days_protection"`
	ProtectionPercentage int  `json:"protection_percentage"`
	MinProtectedFeatures int  `json:"min_protected_features"`
	MaxProtectedFeatures int  `json:"max_protected_features"`
}

// GetProtectionConfig 获取保护配置
func (sp *SimpleProtector) GetProtectionConfig() *ProtectionConfig {
	return &ProtectionConfig{
		Enabled:              true, // 默认启用
		RecentDaysProtection: 7,    // 保护最近7天
		ProtectionPercentage: 20,   // 保护20%
		MinProtectedFeatures: 10,   // 最少保护10个
		MaxProtectedFeatures: 100,  // 最多保护100个
	}
}

// ShouldEvolve 判断是否应该进行演化（考虑保护机制）
func (sp *SimpleProtector) ShouldEvolve(ctx context.Context, userID, catID string, currentFeatureCount int) (bool, string) {
	maxFeatures := sp.config.MaxFeaturesPerCat

	// 如果特征数量未超过限制，不需要演化
	if currentFeatureCount <= maxFeatures {
		return false, "feature count within limit"
	}

	// 检查是否有用户状态标记（如果实现了用户反馈系统）
	if sp.hasActiveUserStateMarker(ctx, userID, catID) {
		return false, "user marked temporary state - evolution paused"
	}

	// 检查最近是否有大量新特征（可能表示状态变化）
	recentFeatureCount := sp.getRecentFeatureCount(ctx, userID, catID, 3*24*time.Hour) // 最近3天
	if recentFeatureCount > maxFeatures/4 {                                            // 如果最近3天的特征超过总量的25%
		log.Printf("High recent feature activity for cat %s, delaying evolution", catID)
		return false, "high recent activity - possible state change"
	}

	return true, "evolution needed"
}

// GetEvolutionCandidates 获取可以删除的特征候选（保护重要特征）
func (sp *SimpleProtector) GetEvolutionCandidates(
	ctx context.Context,
	userID, catID string,
	allFeatures []*SimpleFeature,
	targetCount int,
) ([]*SimpleFeature, error) {

	if len(allFeatures) <= targetCount {
		return []*SimpleFeature{}, nil
	}

	config := sp.GetProtectionConfig()

	// 1. 识别需要保护的特征
	protectedFeatures := sp.identifyProtectedFeatures(allFeatures, config)

	// 2. 获取可删除的特征
	deletableFeatures := sp.getDeletableFeatures(allFeatures, protectedFeatures)

	// 3. 按删除优先级排序
	sp.sortByDeletionPriority(deletableFeatures)

	// 4. 计算需要删除的数量
	needToDelete := len(allFeatures) - targetCount

	if len(deletableFeatures) < needToDelete {
		log.Printf("Warning: only %d deletable features available, need to delete %d",
			len(deletableFeatures), needToDelete)
		return deletableFeatures, nil
	}

	candidates := deletableFeatures[:needToDelete]

	log.Printf("Evolution plan for cat %s: total=%d, target=%d, protected=%d, candidates=%d",
		catID, len(allFeatures), targetCount, len(protectedFeatures), len(candidates))

	return candidates, nil
}

// identifyProtectedFeatures 识别需要保护的特征
func (sp *SimpleProtector) identifyProtectedFeatures(allFeatures []*SimpleFeature, config *ProtectionConfig) []*SimpleFeature {
	if !config.Enabled {
		return []*SimpleFeature{}
	}

	protected := make([]*SimpleFeature, 0)

	// 1. 保护所有参考特征（初始化时的特征）
	for _, feature := range allFeatures {
		if feature.IsReference {
			protected = append(protected, feature)
		}
	}

	// 2. 保护最近N天内的高质量特征
	recentCutoff := time.Now().Add(-time.Duration(config.RecentDaysProtection) * 24 * time.Hour)
	recentFeatures := make([]*SimpleFeature, 0)

	for _, feature := range allFeatures {
		if feature.Timestamp.After(recentCutoff) && !feature.IsReference {
			recentFeatures = append(recentFeatures, feature)
		}
	}

	// 按质量排序最近的特征
	sort.Slice(recentFeatures, func(i, j int) bool {
		return recentFeatures[i].QualityScore > recentFeatures[j].QualityScore
	})

	// 保护指定百分比的高质量最近特征
	protectCount := len(recentFeatures) * config.ProtectionPercentage / 100
	if protectCount < config.MinProtectedFeatures {
		protectCount = config.MinProtectedFeatures
	}
	if protectCount > config.MaxProtectedFeatures {
		protectCount = config.MaxProtectedFeatures
	}
	if protectCount > len(recentFeatures) {
		protectCount = len(recentFeatures)
	}

	for i := 0; i < protectCount; i++ {
		protected = append(protected, recentFeatures[i])
	}

	log.Printf("Protected features: %d reference + %d recent high-quality = %d total",
		sp.countReferenceFeatures(allFeatures), protectCount, len(protected))

	return protected
}

// getDeletableFeatures 获取可删除的特征
func (sp *SimpleProtector) getDeletableFeatures(allFeatures, protectedFeatures []*SimpleFeature) []*SimpleFeature {
	protectedMap := make(map[string]bool)
	for _, pf := range protectedFeatures {
		protectedMap[pf.ID] = true
	}

	deletable := make([]*SimpleFeature, 0)
	for _, feature := range allFeatures {
		if !protectedMap[feature.ID] {
			deletable = append(deletable, feature)
		}
	}

	return deletable
}

// sortByDeletionPriority 按删除优先级排序
func (sp *SimpleProtector) sortByDeletionPriority(features []*SimpleFeature) {
	sort.Slice(features, func(i, j int) bool {
		a, b := features[i], features[j]

		// 1. 质量低的优先删除
		if math.Abs(a.QualityScore-b.QualityScore) > 0.1 {
			return a.QualityScore < b.QualityScore
		}

		// 2. 时间老的优先删除
		if a.Timestamp.Before(b.Timestamp.Add(-24 * time.Hour)) {
			return true
		}
		if b.Timestamp.Before(a.Timestamp.Add(-24 * time.Hour)) {
			return false
		}

		// 3. 置信度低的优先删除
		return a.Confidence < b.Confidence
	})
}

// 辅助方法
func (sp *SimpleProtector) countReferenceFeatures(features []*SimpleFeature) int {
	count := 0
	for _, f := range features {
		if f.IsReference {
			count++
		}
	}
	return count
}

func (sp *SimpleProtector) hasActiveUserStateMarker(ctx context.Context, userID, catID string) bool {
	// 使用UserFeedbackDetector检查活跃状态
	if sp.userFeedbackDetector != nil {
		marker, err := sp.userFeedbackDetector.getActiveStateMarker(ctx, userID, catID)
		if err != nil {
			log.Printf("Warning: failed to check user state marker: %v", err)
			return false
		}
		return marker != nil
	}
	return false
}

func (sp *SimpleProtector) getRecentFeatureCount(ctx context.Context, userID, catID string, duration time.Duration) int {
	if sp.qdrantClient == nil {
		log.Printf("Warning: Qdrant client not initialized")
		return 0
	}

	// 计算时间范围
	endTime := time.Now()
	startTime := endTime.Add(-duration)

	// 使用虚拟查询向量获取特征
	queryVector := make([]float64, 512)
	searchResults, err := sp.qdrantClient.SearchSimilarCatFeatures(ctx, userID, queryVector, 1000)
	if err != nil {
		log.Printf("Warning: failed to search features: %v", err)
		return 0
	}

	// 统计时间范围内的特征数量
	count := 0
	for _, result := range searchResults {
		// 检查cat_id匹配
		if resultCatID, ok := result.Payload["cat_id"].(string); ok && resultCatID == catID {
			// 检查时间范围
			if timestampStr, ok := result.Payload["timestamp"].(string); ok {
				if timestamp, err := time.Parse(time.RFC3339, timestampStr); err == nil {
					if timestamp.After(startTime) && timestamp.Before(endTime) {
						count++
					}
				}
			}
		}
	}

	log.Printf("Found %d recent features for cat %s in last %v", count, catID, duration)
	return count
}

// ConvertQdrantFeatures 将Qdrant搜索结果转换为SimpleFeature
func (sp *SimpleProtector) ConvertQdrantFeatures(results []qdrant.SearchResult) []*SimpleFeature {
	features := make([]*SimpleFeature, len(results))

	for i, result := range results {
		feature := &SimpleFeature{
			ID:     sp.extractStringFromPayload(result.Payload, "original_id", "unknown"),
			CatID:  sp.extractStringFromPayload(result.Payload, "cat_id", "unknown"),
			UserID: sp.extractStringFromPayload(result.Payload, "user_id", "unknown"),
		}

		// 提取数值字段
		feature.Similarity = sp.extractFloatFromPayload(result.Payload, "similarity", 0.0)
		feature.Confidence = sp.extractFloatFromPayload(result.Payload, "confidence", 0.0)

		// 提取时间戳
		if timestampStr := sp.extractStringFromPayload(result.Payload, "timestamp", ""); timestampStr != "" {
			if ts, err := time.Parse(time.RFC3339, timestampStr); err == nil {
				feature.Timestamp = ts
			} else {
				feature.Timestamp = time.Now()
			}
		} else {
			feature.Timestamp = time.Now()
		}

		// 提取是否为参考特征
		feature.IsReference = sp.extractBoolFromPayload(result.Payload, "is_reference", false)

		// 计算质量得分（简化版本）
		feature.QualityScore = (feature.Similarity + feature.Confidence) / 2.0

		features[i] = feature
	}

	return features
}

// 辅助方法：从payload提取字段
func (sp *SimpleProtector) extractStringFromPayload(payload map[string]interface{}, key, defaultValue string) string {
	if val, ok := payload[key].(string); ok {
		return val
	}
	return defaultValue
}

func (sp *SimpleProtector) extractFloatFromPayload(payload map[string]interface{}, key string, defaultValue float64) float64 {
	if val, ok := payload[key].(float64); ok {
		return val
	}
	return defaultValue
}

func (sp *SimpleProtector) extractBoolFromPayload(payload map[string]interface{}, key string, defaultValue bool) bool {
	if val, ok := payload[key].(bool); ok {
		return val
	}
	return defaultValue
}
