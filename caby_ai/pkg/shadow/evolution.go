package shadow

import (
	"context"
	"fmt"
	"log"
	"math"
	"sort"
	"time"

	"caby-ai/config"
	"caby-ai/pkg/qdrant"
)

// FeatureEvolutionManager 特征演化管理器
type FeatureEvolutionManager struct {
	config       *config.ShadowModeConfig
	qdrantClient *qdrant.QdrantClient
}

// NewFeatureEvolutionManager 创建特征演化管理器
func NewFeatureEvolutionManager(cfg *config.ShadowModeConfig, qdrantClient *qdrant.QdrantClient) *FeatureEvolutionManager {
	return &FeatureEvolutionManager{
		config:       cfg,
		qdrantClient: qdrantClient,
	}
}

// FeatureWeight 特征权重信息
type FeatureWeight struct {
	ID            string    `json:"id"`
	CatID         string    `json:"cat_id"`
	Timestamp     time.Time `json:"timestamp"`
	Similarity    float64   `json:"similarity"`
	MatchCount    int       `json:"match_count"`
	TotalWeight   float64   `json:"total_weight"`
	TimeWeight    float64   `json:"time_weight"`
	QualityWeight float64   `json:"quality_weight"`
	FreqWeight    float64   `json:"freq_weight"`
}

// CalculateFeatureWeight 计算特征权重
func (fem *FeatureEvolutionManager) CalculateFeatureWeight(
	timestamp time.Time,
	similarity float64,
	matchCount int,
	totalMatches int,
) *FeatureWeight {
	now := time.Now()
	ageDays := now.Sub(timestamp).Hours() / 24

	// 时间权重：指数衰减
	timeWeight := math.Exp(-ageDays / fem.config.TimeDecayFactor)

	// 质量权重：基于相似度
	qualityWeight := similarity

	// 频次权重：基于匹配频率
	freqWeight := 1.0
	if totalMatches > 0 {
		freqWeight = float64(matchCount) / float64(totalMatches)
	}

	// 综合权重计算
	qualityRatio := fem.config.QualityWeightRatio
	timeRatio := (1.0 - qualityRatio) * 0.7
	freqRatio := (1.0 - qualityRatio) * 0.3

	totalWeight := qualityWeight*qualityRatio + timeWeight*timeRatio + freqWeight*freqRatio

	return &FeatureWeight{
		Timestamp:     timestamp,
		Similarity:    similarity,
		MatchCount:    matchCount,
		TotalWeight:   totalWeight,
		TimeWeight:    timeWeight,
		QualityWeight: qualityWeight,
		FreqWeight:    freqWeight,
	}
}

// ShouldEvolveCat 判断是否需要对某只猫进行特征演化
func (fem *FeatureEvolutionManager) ShouldEvolveCat(ctx context.Context, userID, catID string) (bool, error) {
	// 检查上次演化时间
	lastEvolution, err := fem.getLastEvolutionTime(ctx, userID, catID)
	if err != nil {
		log.Printf("Warning: failed to get last evolution time for cat %s: %v", catID, err)
		// 如果无法获取上次演化时间，假设需要演化
		return true, nil
	}

	// 检查是否到了演化周期
	now := time.Now()
	evolutionInterval := time.Duration(fem.config.EvolutionCycle) * time.Hour

	if now.Sub(lastEvolution) >= evolutionInterval {
		log.Printf("Cat %s needs evolution: last evolution was %v ago",
			catID, now.Sub(lastEvolution))
		return true, nil
	}

	return false, nil
}

// EvolveCatFeatures 对指定猫咪的特征进行演化
func (fem *FeatureEvolutionManager) EvolveCatFeatures(ctx context.Context, userID, catID string) error {
	log.Printf("Starting feature evolution for cat %s (user %s)", catID, userID)

	// 1. 获取该猫咪的所有特征
	features, err := fem.getCatFeatures(ctx, userID, catID)
	if err != nil {
		return fmt.Errorf("failed to get cat features: %w", err)
	}

	if len(features) <= fem.config.MaxFeaturesPerCat {
		log.Printf("Cat %s has %d features, no evolution needed (max: %d)",
			catID, len(features), fem.config.MaxFeaturesPerCat)
		return nil
	}

	// 2. 计算每个特征的权重
	weights := make([]*FeatureWeight, len(features))
	totalMatches := fem.calculateTotalMatches(features)

	for i, feature := range features {
		timestamp := fem.parseTimestamp(feature.Payload)
		similarity := fem.parseSimilarity(feature.Payload)
		matchCount := fem.parseMatchCount(feature.Payload)

		weights[i] = fem.CalculateFeatureWeight(timestamp, similarity, matchCount, totalMatches)

		// 类型断言处理ID
		if idStr, ok := feature.ID.(string); ok {
			weights[i].ID = idStr
		} else {
			weights[i].ID = fmt.Sprintf("%v", feature.ID)
		}
		weights[i].CatID = catID
	}

	// 3. 根据演化策略选择要保留的特征
	keepFeatures := fem.selectFeaturesForEvolution(weights)

	// 4. 删除不需要的特征
	toDelete := fem.findFeaturesToDelete(weights, keepFeatures)
	if len(toDelete) > 0 {
		err = fem.deleteFeatures(ctx, userID, toDelete)
		if err != nil {
			return fmt.Errorf("failed to delete features: %w", err)
		}
		log.Printf("Evolved cat %s: deleted %d features, kept %d features",
			catID, len(toDelete), len(keepFeatures))
	}

	// 5. 记录演化时间
	err = fem.recordEvolutionTime(ctx, userID, catID)
	if err != nil {
		log.Printf("Warning: failed to record evolution time: %v", err)
	}

	return nil
}

// selectFeaturesForEvolution 根据演化策略选择要保留的特征
func (fem *FeatureEvolutionManager) selectFeaturesForEvolution(weights []*FeatureWeight) []*FeatureWeight {
	maxFeatures := fem.config.MaxFeaturesPerCat

	switch fem.config.EvolutionStrategy {
	case "time_based":
		return fem.selectByTime(weights, maxFeatures)
	case "quality_based":
		return fem.selectByQuality(weights, maxFeatures)
	case "hybrid":
		return fem.selectByHybrid(weights, maxFeatures)
	default:
		log.Printf("Unknown evolution strategy: %s, using hybrid", fem.config.EvolutionStrategy)
		return fem.selectByHybrid(weights, maxFeatures)
	}
}

// selectByTime 基于时间选择特征（保留最新的）
func (fem *FeatureEvolutionManager) selectByTime(weights []*FeatureWeight, maxFeatures int) []*FeatureWeight {
	sort.Slice(weights, func(i, j int) bool {
		return weights[i].Timestamp.After(weights[j].Timestamp)
	})

	if len(weights) <= maxFeatures {
		return weights
	}
	return weights[:maxFeatures]
}

// selectByQuality 基于质量选择特征（保留高质量的）
func (fem *FeatureEvolutionManager) selectByQuality(weights []*FeatureWeight, maxFeatures int) []*FeatureWeight {
	sort.Slice(weights, func(i, j int) bool {
		return weights[i].QualityWeight > weights[j].QualityWeight
	})

	if len(weights) <= maxFeatures {
		return weights
	}
	return weights[:maxFeatures]
}

// selectByHybrid 混合策略选择特征（综合权重）
func (fem *FeatureEvolutionManager) selectByHybrid(weights []*FeatureWeight, maxFeatures int) []*FeatureWeight {
	sort.Slice(weights, func(i, j int) bool {
		return weights[i].TotalWeight > weights[j].TotalWeight
	})

	if len(weights) <= maxFeatures {
		return weights
	}
	return weights[:maxFeatures]
}

// 辅助方法（占位符实现）
func (fem *FeatureEvolutionManager) getLastEvolutionTime(ctx context.Context, userID, catID string) (time.Time, error) {
	// TODO: 实现从存储中获取上次演化时间
	// 可以存储在Qdrant的元数据中或单独的存储系统中
	return time.Now().Add(-time.Duration(fem.config.EvolutionCycle+1) * time.Hour), nil
}

func (fem *FeatureEvolutionManager) getCatFeatures(ctx context.Context, userID, catID string) ([]qdrant.SearchResult, error) {
	if fem.qdrantClient == nil {
		return []qdrant.SearchResult{}, fmt.Errorf("qdrant client not initialized")
	}

	// 使用虚拟查询向量获取所有特征
	queryVector := make([]float64, 512)
	searchResults, err := fem.qdrantClient.SearchSimilarCatFeatures(ctx, userID, queryVector, 10000) // 大limit获取所有特征
	if err != nil {
		return []qdrant.SearchResult{}, fmt.Errorf("failed to search features: %w", err)
	}

	// 过滤出指定猫咪的特征
	var catFeatures []qdrant.SearchResult
	for _, result := range searchResults {
		if resultCatID, ok := result.Payload["cat_id"].(string); ok && resultCatID == catID {
			catFeatures = append(catFeatures, result)
		}
	}

	log.Printf("Retrieved %d features for cat %s", len(catFeatures), catID)
	return catFeatures, nil
}

func (fem *FeatureEvolutionManager) calculateTotalMatches(features []qdrant.SearchResult) int {
	// TODO: 计算总匹配次数
	return len(features)
}

func (fem *FeatureEvolutionManager) parseTimestamp(payload map[string]interface{}) time.Time {
	if ts, ok := payload["timestamp"].(string); ok {
		if t, err := time.Parse(time.RFC3339, ts); err == nil {
			return t
		}
	}
	return time.Now()
}

func (fem *FeatureEvolutionManager) parseSimilarity(payload map[string]interface{}) float64 {
	if sim, ok := payload["similarity"].(float64); ok {
		return sim
	}
	return 0.0
}

func (fem *FeatureEvolutionManager) parseMatchCount(payload map[string]interface{}) int {
	if count, ok := payload["match_count"].(float64); ok {
		return int(count)
	}
	return 1
}

func (fem *FeatureEvolutionManager) findFeaturesToDelete(all, keep []*FeatureWeight) []string {
	keepMap := make(map[string]bool)
	for _, feature := range keep {
		keepMap[feature.ID] = true
	}

	var toDelete []string
	for _, feature := range all {
		if !keepMap[feature.ID] {
			toDelete = append(toDelete, feature.ID)
		}
	}
	return toDelete
}

func (fem *FeatureEvolutionManager) deleteFeatures(ctx context.Context, userID string, featureIDs []string) error {
	// TODO: 实现批量删除特征
	log.Printf("Would delete %d features for user %s", len(featureIDs), userID)
	return nil
}

func (fem *FeatureEvolutionManager) recordEvolutionTime(ctx context.Context, userID, catID string) error {
	// 在实际实现中，这里可以将演化时间记录到数据库或Qdrant的metadata中
	// 目前只记录日志
	evolutionTime := time.Now()
	log.Printf("Recording evolution time for cat %s (user %s) at %v", catID, userID, evolutionTime)

	// 可以考虑将演化时间存储到Qdrant的特殊collection中
	// 或者作为特征的metadata更新

	return nil
}
