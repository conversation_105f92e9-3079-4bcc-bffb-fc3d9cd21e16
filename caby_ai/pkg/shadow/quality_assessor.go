package shadow

import (
	"context"
	"math"
	"time"

	"caby-ai/config"
	"caby-ai/pkg/vision"
)

// QualityAssessor 特征质量评估器
type QualityAssessor struct {
	config *config.ShadowModeConfig
}

// NewQualityAssessor 创建质量评估器
func NewQualityAssessor(cfg *config.ShadowModeConfig) *QualityAssessor {
	return &QualityAssessor{
		config: cfg,
	}
}

// QualityMetrics 质量评估指标
type QualityMetrics struct {
	// 基础指标
	Similarity float64 `json:"similarity"` // 余弦相似度
	Confidence float64 `json:"confidence"` // 模型置信度

	// 稳定性指标
	ConsistencyScore float64 `json:"consistency"` // 一致性得分
	VarianceScore    float64 `json:"variance"`    // 方差得分

	// 上下文指标
	EnvironmentScore float64 `json:"environment"` // 环境质量得分
	LightingScore    float64 `json:"lighting"`    // 光照质量得分
	ClarityScore     float64 `json:"clarity"`     // 清晰度得分

	// 时间指标
	RecencyScore   float64 `json:"recency"`   // 时效性得分
	FrequencyScore float64 `json:"frequency"` // 频次得分

	// 综合指标
	OverallQuality float64 `json:"overall_quality"` // 综合质量得分
	QualityTier    string  `json:"quality_tier"`    // 质量等级
}

// AssessFeatureQuality 评估特征质量
func (qa *QualityAssessor) AssessFeatureQuality(
	ctx context.Context,
	similarity float64,
	confidence float64,
	featuresResp *vision.FeaturedResponse,
	historicalData *HistoricalData,
) *QualityMetrics {

	metrics := &QualityMetrics{
		Similarity: similarity,
		Confidence: confidence,
	}

	// 1. 计算稳定性指标
	metrics.ConsistencyScore = qa.calculateConsistencyScore(similarity, confidence)
	metrics.VarianceScore = qa.calculateVarianceScore(featuresResp, historicalData)

	// 2. 计算上下文指标
	metrics.EnvironmentScore = qa.calculateEnvironmentScore(featuresResp)
	metrics.LightingScore = qa.calculateLightingScore(featuresResp)
	metrics.ClarityScore = qa.calculateClarityScore(featuresResp)

	// 3. 计算时间指标
	metrics.RecencyScore = qa.calculateRecencyScore(time.Now())
	metrics.FrequencyScore = qa.calculateFrequencyScore(historicalData)

	// 4. 计算综合质量得分
	metrics.OverallQuality = qa.calculateOverallQuality(metrics)
	metrics.QualityTier = qa.classifyQualityTier(metrics.OverallQuality)

	return metrics
}

// calculateConsistencyScore 计算一致性得分
func (qa *QualityAssessor) calculateConsistencyScore(similarity, confidence float64) float64 {
	// 相似度和置信度的一致性：两者应该相近
	diff := math.Abs(similarity - confidence)
	consistency := 1.0 - diff

	// 确保在[0,1]范围内
	if consistency < 0 {
		consistency = 0
	}

	return consistency
}

// calculateVarianceScore 计算方差得分
func (qa *QualityAssessor) calculateVarianceScore(featuresResp *vision.FeaturedResponse, historical *HistoricalData) float64 {
	if historical == nil || len(historical.RecentFeatures) < 3 {
		return 0.5 // 默认中等得分
	}

	// 计算特征向量的方差
	// 低方差表示特征稳定，高方差可能表示噪声或异常
	variance := qa.calculateFeatureVariance(featuresResp.Results.Features, historical.RecentFeatures)

	// 将方差转换为得分：适中的方差最好
	optimalVariance := 0.1
	varianceScore := 1.0 - math.Abs(variance-optimalVariance)/optimalVariance

	if varianceScore < 0 {
		varianceScore = 0
	}
	if varianceScore > 1 {
		varianceScore = 1
	}

	return varianceScore
}

// calculateEnvironmentScore 计算环境质量得分
func (qa *QualityAssessor) calculateEnvironmentScore(featuresResp *vision.FeaturedResponse) float64 {
	// 基于特征向量的分布特性判断环境质量
	// 这里需要根据实际的特征向量结构来实现

	if featuresResp == nil || featuresResp.Results == nil {
		return 0.3
	}

	features := featuresResp.Results.Features
	if len(features) == 0 {
		return 0.3
	}

	// 计算特征向量的统计特性
	mean := qa.calculateMean(features)
	std := qa.calculateStd(features, mean)

	// 好的环境通常有适中的均值和标准差
	environmentScore := 1.0 - math.Abs(mean-0.5) - math.Abs(std-0.2)

	if environmentScore < 0.2 {
		environmentScore = 0.2
	}
	if environmentScore > 1.0 {
		environmentScore = 1.0
	}

	return environmentScore
}

// calculateLightingScore 计算光照质量得分
func (qa *QualityAssessor) calculateLightingScore(featuresResp *vision.FeaturedResponse) float64 {
	// 基于特征向量的亮度相关特征判断光照质量
	// 这是一个简化实现，实际应该基于图像的亮度分析

	if featuresResp == nil || featuresResp.Results == nil {
		return 0.5
	}

	features := featuresResp.Results.Features
	if len(features) < 10 {
		return 0.5
	}

	// 假设前几个特征与亮度相关
	brightnessFeatures := features[:10]
	brightness := qa.calculateMean(brightnessFeatures)

	// 适中的亮度最好
	lightingScore := 1.0 - math.Abs(brightness-0.5)*2

	if lightingScore < 0.2 {
		lightingScore = 0.2
	}

	return lightingScore
}

// calculateClarityScore 计算清晰度得分
func (qa *QualityAssessor) calculateClarityScore(featuresResp *vision.FeaturedResponse) float64 {
	// 基于特征向量的锐度相关特征判断清晰度

	if featuresResp == nil || featuresResp.Results == nil {
		return 0.5
	}

	features := featuresResp.Results.Features
	if len(features) < 20 {
		return 0.5
	}

	// 假设中间部分特征与清晰度相关
	clarityFeatures := features[10:20]
	clarity := qa.calculateStd(clarityFeatures, qa.calculateMean(clarityFeatures))

	// 高标准差通常表示更清晰的图像
	clarityScore := math.Min(clarity*5, 1.0)

	return clarityScore
}

// calculateRecencyScore 计算时效性得分
func (qa *QualityAssessor) calculateRecencyScore(timestamp time.Time) float64 {
	// 越新的特征得分越高
	now := time.Now()
	ageHours := now.Sub(timestamp).Hours()

	// 24小时内得分最高，之后逐渐衰减
	if ageHours <= 24 {
		return 1.0
	}

	// 指数衰减
	recencyScore := math.Exp(-ageHours / (24 * 7)) // 一周衰减因子

	return recencyScore
}

// calculateFrequencyScore 计算频次得分
func (qa *QualityAssessor) calculateFrequencyScore(historical *HistoricalData) float64 {
	if historical == nil {
		return 0.5
	}

	// 基于历史匹配频次计算得分
	totalMatches := historical.TotalMatches
	recentMatches := historical.RecentMatches

	if totalMatches == 0 {
		return 0.3
	}

	// 最近匹配频率
	frequencyRatio := float64(recentMatches) / float64(totalMatches)

	// 适中的频率最好（既不太少也不太多）
	optimalFrequency := 0.3
	frequencyScore := 1.0 - math.Abs(frequencyRatio-optimalFrequency)/optimalFrequency

	if frequencyScore < 0.2 {
		frequencyScore = 0.2
	}

	return frequencyScore
}

// calculateOverallQuality 计算综合质量得分
func (qa *QualityAssessor) calculateOverallQuality(metrics *QualityMetrics) float64 {
	// 加权平均计算综合质量
	weights := map[string]float64{
		"similarity":  0.25, // 相似度权重
		"confidence":  0.20, // 置信度权重
		"consistency": 0.15, // 一致性权重
		"environment": 0.15, // 环境权重
		"lighting":    0.10, // 光照权重
		"clarity":     0.10, // 清晰度权重
		"recency":     0.03, // 时效性权重
		"frequency":   0.02, // 频次权重
	}

	overallQuality := 0.0
	overallQuality += metrics.Similarity * weights["similarity"]
	overallQuality += metrics.Confidence * weights["confidence"]
	overallQuality += metrics.ConsistencyScore * weights["consistency"]
	overallQuality += metrics.EnvironmentScore * weights["environment"]
	overallQuality += metrics.LightingScore * weights["lighting"]
	overallQuality += metrics.ClarityScore * weights["clarity"]
	overallQuality += metrics.RecencyScore * weights["recency"]
	overallQuality += metrics.FrequencyScore * weights["frequency"]

	return overallQuality
}

// classifyQualityTier 分类质量等级
func (qa *QualityAssessor) classifyQualityTier(overallQuality float64) string {
	if overallQuality >= 0.8 {
		return "excellent"
	} else if overallQuality >= 0.65 {
		return "good"
	} else if overallQuality >= 0.45 {
		return "fair"
	} else {
		return "poor"
	}
}

// 辅助方法
func (qa *QualityAssessor) calculateMean(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}

	sum := 0.0
	for _, v := range values {
		sum += v
	}
	return sum / float64(len(values))
}

func (qa *QualityAssessor) calculateStd(values []float64, mean float64) float64 {
	if len(values) <= 1 {
		return 0
	}

	sumSquares := 0.0
	for _, v := range values {
		diff := v - mean
		sumSquares += diff * diff
	}

	variance := sumSquares / float64(len(values)-1)
	return math.Sqrt(variance)
}

func (qa *QualityAssessor) calculateFeatureVariance(current []float64, historical [][]float64) float64 {
	// 简化实现：计算当前特征与历史特征的平均差异
	if len(historical) == 0 || len(current) == 0 {
		return 0.5
	}

	totalDiff := 0.0
	count := 0

	for _, hist := range historical {
		if len(hist) == len(current) {
			diff := 0.0
			for i := range current {
				diff += math.Abs(current[i] - hist[i])
			}
			totalDiff += diff / float64(len(current))
			count++
		}
	}

	if count == 0 {
		return 0.5
	}

	return totalDiff / float64(count)
}

// HistoricalData 历史数据结构
type HistoricalData struct {
	RecentFeatures [][]float64 `json:"recent_features"`
	TotalMatches   int         `json:"total_matches"`
	RecentMatches  int         `json:"recent_matches"`
}
