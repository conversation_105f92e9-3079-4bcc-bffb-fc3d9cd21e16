package api

import (
	"net/http"
	"io"
	"bytes"
	"log"
	"encoding/json"

	"context" // Import context

	"caby-ai/pkg/models" // Import analysis service

	"github.com/gin-gonic/gin"
)

// Define AnalysisService interface
type AnalysisService interface {
	AnalyzeRecord(ctx context.Context, record *models.RecordShit) (*models.RecordAnalysis, error)
}

type AnalysisHandler struct {
	service AnalysisService // Use the interface
}

// NewAnalysisHandler constructor
func NewAnalysisHandler(service AnalysisService) *AnalysisHandler {
	return &AnalysisHandler{service: service}
}

// HandleAnalysisRequest godoc
// @Summary Trigger video analysis for a record
// @Description Receives record details, fetches video, performs analysis, stores results in Weaviate, and returns analysis summary.
// @Tags Analysis
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer Service Token"
// @Param record body models.RecordShit true "Record details for analysis"
// @Success 200 {object} models.RecordAnalysis "Analysis completed successfully (placeholder)" // Updated success description
// @Failure 400 {object} gin.H "Invalid input data"
// @Failure 401 {object} gin.H "Unauthorized"
// @Failure 500 {object} gin.H "Internal server error during analysis"
// @Router /api/v1/analyze [post]
func (h *AnalysisHandler) HandleAnalysisRequest(c *gin.Context) {
	// 添加请求诊断
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		log.Printf("Error reading request body: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request body: " + err.Error()})
		return
	}

	// 重要：恢复请求体，否则后续的ShouldBindJSON会失败
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	// 记录请求详情便于排查
	log.Printf("Request Content-Length: %d, Actual body length: %d",
		c.Request.ContentLength, len(body))

	if len(body) == 0 {
		log.Printf("Warning: Empty request body received")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Empty request body"})
		return
	}

	// Debug: 打印完整请求体
	log.Printf("Request body: %s", string(body))

	// 尝试手动解析JSON，查看可能的问题
	var rawInput map[string]interface{}
	if err := json.Unmarshal(body, &rawInput); err != nil {
		log.Printf("JSON parse error (raw): %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON format: " + err.Error()})
		return
	}

	// 打印期望的字段和实际字段
	expectedFields := []string{"video_id", "device_id", "start_time", "end_time", "video_path"}
	log.Printf("Expected fields check:")
	for _, field := range expectedFields {
		val, exists := rawInput[field]
		log.Printf("- Field '%s': exists=%v, value=%v", field, exists, val)
	}

	var input models.RecordShit
	if err := json.Unmarshal(body, &input); err != nil {
		log.Printf("JSON binding error (struct): %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse JSON into RecordShit: " + err.Error()})
		return
	}

	// 打印解析后的结构体关键字段
	log.Printf("Parsed object: VideoID=%s, DeviceID=%s", input.VideoID, input.DeviceID)

	// Validate input further if necessary
	if input.VideoID == "" || input.DeviceID == "" {
		log.Printf("Required fields missing: VideoID=%s, DeviceID=%s", input.VideoID, input.DeviceID)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing required fields: video_id or device_id"})
		return
	}

	// Call the analysis service
	log.Printf("Calling AnalyzeRecord for video %s from device %s", input.VideoID, input.DeviceID)
	analysisResult, err := h.service.AnalyzeRecord(c.Request.Context(), &input) // Pass context
	if err != nil {
		// Distinguish between fetch errors and analysis errors if needed
		log.Printf("Analysis failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Analysis failed: " + err.Error()})
		return
	}

	log.Printf("Analysis completed successfully for video %s", input.VideoID)
	// Return the placeholder result from the service
	c.JSON(http.StatusOK, analysisResult)
}
