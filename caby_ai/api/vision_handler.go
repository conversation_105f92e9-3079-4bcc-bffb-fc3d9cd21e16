package api

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"caby-ai/config"
	"caby-ai/pkg/vision"

	"github.com/gin-gonic/gin"
)

// VisionHandler Vision处理器结构
type VisionHandler struct {
	client *vision.Client
	config *config.Config
}

// NewVisionHandler 创建新的Vision处理器
func NewVisionHandler(cfg *config.Config) *VisionHandler {
	// 从环境变量获取API密钥，如果没有则使用配置文件
	apiKey := os.Getenv("VISION_API_KEY")
	if apiKey == "" {
		apiKey = cfg.Vision.ApiKey
	}

	// 构建base URL
	baseURL := fmt.Sprintf("http://%s:%d", cfg.Vision.Host, cfg.Vision.Port)

	// 创建客户端
	client := vision.NewClient(baseURL, apiKey)

	// 设置超时时间
	if cfg.Vision.Timeout > 0 {
		client.SetTimeout(time.Duration(cfg.Vision.Timeout) * time.Second)
	}

	return &VisionHandler{
		client: client,
		config: cfg,
	}
}

// FaceDetection 人脸检测结果结构
type FaceDetection struct {
	BBox       []int     `json:"bbox"`                // [x1, y1, x2, y2]
	Confidence float64   `json:"confidence"`          // 置信度
	Landmarks  []float64 `json:"landmarks,omitempty"` // 关键点
	Aligned    bool      `json:"aligned,omitempty"`   // 是否已对齐
}

// FaceEmbedding 人脸嵌入向量结构
type FaceEmbedding struct {
	FaceID     int       `json:"face_id"`
	Embedding  []float64 `json:"embedding"`
	BBox       []int     `json:"bbox"`
	Confidence float64   `json:"confidence"`
}

// DetectionResponse API响应结构
type DetectionResponse struct {
	Success     bool            `json:"success"`
	Faces       []FaceDetection `json:"faces,omitempty"`        // 人脸检测结果
	Embeddings  []FaceEmbedding `json:"embeddings,omitempty"`   // 人脸嵌入向量
	FaceCount   int             `json:"face_count,omitempty"`   // 人脸数量
	HasFaces    bool            `json:"has_faces,omitempty"`    // 是否检测到人脸
	ProcessTime float64         `json:"process_time,omitempty"` // 处理时间(毫秒)
	Task        string          `json:"task,omitempty"`         // 任务类型
	Message     string          `json:"message"`
	Error       string          `json:"error,omitempty"`
	RequestID   string          `json:"request_id,omitempty"`
}

// CatDetectionResponse 猫咪检测API响应结构
type CatDetectionResponse struct {
	Success            bool               `json:"success"`
	PredictedCat       string             `json:"predicted_cat,omitempty"`       // 预测的猫咪名称
	Confidence         float64            `json:"confidence,omitempty"`          // 置信度
	ClassProbabilities map[string]float64 `json:"class_probabilities,omitempty"` // 类别概率
	ProcessTime        float64            `json:"process_time,omitempty"`        // 处理时间(毫秒)
	Task               string             `json:"task,omitempty"`                // 任务类型
	Message            string             `json:"message"`
	Error              string             `json:"error,omitempty"`
	RequestID          string             `json:"request_id,omitempty"`
}

// HandleFaceDetection 处理人脸检测请求
func (h *VisionHandler) HandleFaceDetection(c *gin.Context) {
	startTime := time.Now()

	// 生成请求ID用于日志追踪
	requestID := c.GetHeader("X-Request-ID")
	if requestID == "" {
		requestID = fmt.Sprintf("face_req_%d", time.Now().UnixNano())
	}

	// 获取上传的文件
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, DetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to get image file: %v", err),
			RequestID: requestID,
		})
		return
	}
	defer file.Close()

	// 验证文件类型
	if !isValidImageType(header.Filename) {
		c.JSON(http.StatusBadRequest, DetectionResponse{
			Success:   false,
			Error:     "Invalid image type. Only JPG, JPEG, PNG, BMP are supported",
			RequestID: requestID,
		})
		return
	}

	// 读取文件内容
	fileBytes, err := io.ReadAll(file)
	if err != nil {
		c.JSON(http.StatusInternalServerError, DetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to read image file: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 获取任务类型和参数
	task := c.DefaultPostForm("task", "detect")
	confThreshold, _ := strconv.ParseFloat(c.DefaultPostForm("conf_threshold", "0.2"), 64)
	iouThreshold, _ := strconv.ParseFloat(c.DefaultPostForm("iou_threshold", "0.5"), 64)
	returnAligned := c.DefaultPostForm("return_aligned", "true") == "true"

	// 将图像数据转换为 base64 格式
	imageBase64 := base64.StdEncoding.EncodeToString(fileBytes)

	// 根据任务类型选择正确的API端点
	var apiEndpoint string
	if task == "embeddings" {
		apiEndpoint = "/embeddings"
	} else {
		apiEndpoint = "/detect"
	}

	// 调用Vision API
	url := fmt.Sprintf("http://%s:%d%s", h.config.Vision.Host, h.config.Vision.Port, apiEndpoint)

	payload := map[string]interface{}{
		"image":          imageBase64,
		"conf_threshold": confThreshold,
		"iou_threshold":  iouThreshold,
		"return_aligned": returnAligned,
	}

	// 为detect端点添加task字段
	if task != "embeddings" {
		payload["task"] = task
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		c.JSON(http.StatusInternalServerError, DetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to marshal request: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 发送HTTP请求到Vision
	req, err := http.NewRequest("POST", url, strings.NewReader(string(jsonData)))
	if err != nil {
		c.JSON(http.StatusInternalServerError, DetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to create request: %v", err),
			RequestID: requestID,
		})
		return
	}

	req.Header.Set("Content-Type", "application/json")
	// 添加Bearer Token认证
	if h.config.Vision.ApiKey != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", h.config.Vision.ApiKey))
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, DetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Vision request failed: %v", err),
			RequestID: requestID,
		})
		return
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, DetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to read Vision response: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		// FastAPI 返回错误时的格式可能是 {"detail": "error message"}
		var errorResponse map[string]interface{}
		if json.Unmarshal(responseBody, &errorResponse) == nil {
			if detail, ok := errorResponse["detail"].(string); ok {
				c.JSON(http.StatusInternalServerError, DetectionResponse{
					Success:   false,
					Error:     fmt.Sprintf("Vision API error: %s", detail),
					RequestID: requestID,
				})
				return
			}
		}

		c.JSON(http.StatusInternalServerError, DetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Vision returned status %d: %s", resp.StatusCode, string(responseBody)),
			RequestID: requestID,
		})
		return
	}

	// 解析Vision的响应
	var visionResponse map[string]interface{}
	if err := json.Unmarshal(responseBody, &visionResponse); err != nil {
		c.JSON(http.StatusInternalServerError, DetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to parse Vision response: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 检查Vision响应的成功状态
	success, successExists := visionResponse["success"].(bool)
	if successExists && !success {
		errorMsg, _ := visionResponse["error"].(string)
		if errorMsg == "" {
			errorMsg = "Unknown error from Vision"
		}
		c.JSON(http.StatusInternalServerError, DetectionResponse{
			Success:   false,
			Error:     errorMsg,
			RequestID: requestID,
		})
		return
	}

	// 获取结果 - 如果没有success字段，则直接使用整个响应作为results
	var results map[string]interface{}
	if successExists {
		// 如果有success字段，则从results字段获取数据
		resultsData, ok := visionResponse["results"].(map[string]interface{})
		if !ok {
			c.JSON(http.StatusInternalServerError, DetectionResponse{
				Success:   false,
				Error:     "Invalid response format from Vision",
				RequestID: requestID,
			})
			return
		}
		results = resultsData
	} else {
		// 如果没有success字段，则直接使用整个响应
		results = visionResponse
	}

	processTime := float64(time.Since(startTime).Nanoseconds()) / 1e6 // 转换为毫秒

	// 根据任务类型解析结果
	if task == "embeddings" {
		// 解析嵌入向量结果
		embeddings, err := parseEmbeddingsResponse(results)
		if err != nil {
			c.JSON(http.StatusInternalServerError, DetectionResponse{
				Success:   false,
				Error:     fmt.Sprintf("Failed to parse embeddings response: %v", err),
				RequestID: requestID,
			})
			return
		}

		faceCount, _ := results["face_count"].(float64)
		hasFaces, _ := results["has_faces"].(bool)

		c.JSON(http.StatusOK, DetectionResponse{
			Success:     true,
			Embeddings:  embeddings,
			FaceCount:   int(faceCount),
			HasFaces:    hasFaces,
			ProcessTime: processTime,
			Task:        task,
			Message:     fmt.Sprintf("Face embeddings extracted for %d faces", len(embeddings)),
			RequestID:   requestID,
		})
	} else {
		// 解析人脸检测结果
		faces, err := parseFaceDetectionResponse(results)
		if err != nil {
			c.JSON(http.StatusInternalServerError, DetectionResponse{
				Success:   false,
				Error:     fmt.Sprintf("Failed to parse face detection response: %v", err),
				RequestID: requestID,
			})
			return
		}

		faceCount, _ := results["face_count"].(float64)
		hasFaces, _ := results["has_faces"].(bool)

		c.JSON(http.StatusOK, DetectionResponse{
			Success:     true,
			Faces:       faces,
			FaceCount:   int(faceCount),
			HasFaces:    hasFaces,
			ProcessTime: processTime,
			Task:        task,
			Message:     fmt.Sprintf("Face detection completed, found %d faces", len(faces)),
			RequestID:   requestID,
		})
	}
}

// HandleCatDetection 处理猫咪检测请求
func (h *VisionHandler) HandleCatDetection(c *gin.Context) {
	startTime := time.Now()

	// 生成请求ID用于日志追踪
	requestID := c.GetHeader("X-Request-ID")
	if requestID == "" {
		requestID = fmt.Sprintf("cat_req_%d", time.Now().UnixNano())
	}

	// 获取上传的文件
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, CatDetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to get image file: %v", err),
			RequestID: requestID,
		})
		return
	}
	defer file.Close()

	// 验证文件类型
	if !isValidImageType(header.Filename) {
		c.JSON(http.StatusBadRequest, CatDetectionResponse{
			Success:   false,
			Error:     "Invalid image type. Only JPG, JPEG, PNG, BMP are supported",
			RequestID: requestID,
		})
		return
	}

	// 读取文件内容
	fileBytes, err := io.ReadAll(file)
	if err != nil {
		c.JSON(http.StatusInternalServerError, CatDetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to read image file: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 获取任务参数
	task := c.DefaultPostForm("task", "predict")
	returnFeatures := c.DefaultPostForm("return_features", "false") == "true"
	returnConfidence := c.DefaultPostForm("return_confidence", "true") == "true"

	// 将图像数据转换为 base64 格式
	imageBase64 := base64.StdEncoding.EncodeToString(fileBytes)

	// 调用caby_vision的/predict API
	url := fmt.Sprintf("http://%s:%d/predict", h.config.Vision.Host, h.config.Vision.Port)

	payload := map[string]interface{}{
		"image":             imageBase64,
		"return_features":   returnFeatures,
		"return_confidence": returnConfidence,
		"task":              task,
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		c.JSON(http.StatusInternalServerError, CatDetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to marshal request: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 发送HTTP请求到caby_vision
	req, err := http.NewRequest("POST", url, strings.NewReader(string(jsonData)))
	if err != nil {
		c.JSON(http.StatusInternalServerError, CatDetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to create request: %v", err),
			RequestID: requestID,
		})
		return
	}

	req.Header.Set("Content-Type", "application/json")
	// 添加Bearer Token认证
	if h.config.Vision.ApiKey != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", h.config.Vision.ApiKey))
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, CatDetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Vision request failed: %v", err),
			RequestID: requestID,
		})
		return
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, CatDetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to read Vision response: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		// FastAPI 返回错误时的格式可能是 {"detail": "error message"}
		var errorResponse map[string]interface{}
		if json.Unmarshal(responseBody, &errorResponse) == nil {
			if detail, ok := errorResponse["detail"].(string); ok {
				c.JSON(http.StatusInternalServerError, CatDetectionResponse{
					Success:   false,
					Error:     fmt.Sprintf("Vision API error: %s", detail),
					RequestID: requestID,
				})
				return
			}
		}

		c.JSON(http.StatusInternalServerError, CatDetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Vision returned status %d: %s", resp.StatusCode, string(responseBody)),
			RequestID: requestID,
		})
		return
	}

	// 解析caby_vision的响应
	var visionResponse map[string]interface{}
	if err := json.Unmarshal(responseBody, &visionResponse); err != nil {
		c.JSON(http.StatusInternalServerError, CatDetectionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to parse Vision response: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 检查Vision响应的成功状态
	success, successExists := visionResponse["success"].(bool)
	if successExists && !success {
		errorMsg, _ := visionResponse["error"].(string)
		if errorMsg == "" {
			errorMsg = "Unknown error from Vision"
		}
		c.JSON(http.StatusInternalServerError, CatDetectionResponse{
			Success:   false,
			Error:     errorMsg,
			RequestID: requestID,
		})
		return
	}

	// 获取结果
	var results map[string]interface{}
	if successExists {
		// 如果有success字段，则从results字段获取数据
		resultsData, ok := visionResponse["results"].(map[string]interface{})
		if !ok {
			c.JSON(http.StatusInternalServerError, CatDetectionResponse{
				Success:   false,
				Error:     "Invalid response format from Vision",
				RequestID: requestID,
			})
			return
		}
		results = resultsData
	} else {
		// 如果没有success字段，则直接使用整个响应
		results = visionResponse
	}

	processTime := float64(time.Since(startTime).Nanoseconds()) / 1e6 // 转换为毫秒

	// 解析猫咪检测结果
	predictedCat, _ := results["predicted_cat"].(string)
	confidence, _ := results["confidence"].(float64)

	// 解析类别概率
	classProbabilities := make(map[string]float64)
	if classProbsRaw, ok := results["class_probabilities"].(map[string]interface{}); ok {
		for k, v := range classProbsRaw {
			if prob, ok := v.(float64); ok {
				classProbabilities[k] = prob
			}
		}
	}

	c.JSON(http.StatusOK, CatDetectionResponse{
		Success:            true,
		PredictedCat:       predictedCat,
		Confidence:         confidence,
		ClassProbabilities: classProbabilities,
		ProcessTime:        processTime,
		Task:               task,
		Message:            fmt.Sprintf("Cat detection completed, predicted: %s (confidence: %.4f)", predictedCat, confidence),
		RequestID:          requestID,
	})
}

// HandleHealthCheck 处理Vision健康检查
func (h *VisionHandler) HandleHealthCheck(c *gin.Context) {
	// 调用Vision健康检查API
	url := fmt.Sprintf("http://%s:%d/health", h.config.Vision.Host, h.config.Vision.Port)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status":  "unhealthy",
			"error":   "Failed to create HTTP request",
			"service": "vision",
		})
		return
	}

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status":  "unhealthy",
			"error":   err.Error(),
			"service": "vision",
		})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == 200 {
		// 读取响应体
		responseBody, err := io.ReadAll(resp.Body)
		if err == nil {
			var healthResponse map[string]interface{}
			if json.Unmarshal(responseBody, &healthResponse) == nil {
				c.JSON(http.StatusOK, gin.H{
					"status":  "healthy",
					"service": "vision",
					"url":     fmt.Sprintf("http://%s:%d", h.config.Vision.Host, h.config.Vision.Port),
					"details": healthResponse,
				})
				return
			}
		}

		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "vision",
			"url":     fmt.Sprintf("http://%s:%d", h.config.Vision.Host, h.config.Vision.Port),
		})
	} else {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status":  "unhealthy",
			"error":   fmt.Sprintf("HTTP status %d", resp.StatusCode),
			"service": "vision",
		})
	}
}

// isValidImageType 验证图像文件类型
func isValidImageType(filename string) bool {
	validTypes := []string{".jpg", ".jpeg", ".png", ".bmp", ".JPG", ".JPEG", ".PNG", ".BMP"}
	for _, ext := range validTypes {
		if strings.HasSuffix(filename, ext) {
			return true
		}
	}
	return false
}

// parseFaceDetectionResponse 解析人脸检测响应
func parseFaceDetectionResponse(results map[string]interface{}) ([]FaceDetection, error) {
	facesData, ok := results["faces"]
	if !ok {
		return []FaceDetection{}, nil
	}

	facesArray, ok := facesData.([]interface{})
	if !ok {
		return nil, fmt.Errorf("faces field is not an array")
	}

	var faces []FaceDetection
	for _, faceData := range facesArray {
		faceMap, ok := faceData.(map[string]interface{})
		if !ok {
			continue
		}

		var face FaceDetection

		// 解析bbox
		if bboxData, ok := faceMap["bbox"].([]interface{}); ok && len(bboxData) == 4 {
			face.BBox = make([]int, 4)
			for i, v := range bboxData {
				if f, ok := v.(float64); ok {
					face.BBox[i] = int(f)
				}
			}
		}

		// 解析confidence
		if conf, ok := faceMap["confidence"].(float64); ok {
			face.Confidence = conf
		}

		// 解析landmarks
		if landmarksData, ok := faceMap["landmarks"].([]interface{}); ok {
			face.Landmarks = make([]float64, len(landmarksData))
			for i, v := range landmarksData {
				if f, ok := v.(float64); ok {
					face.Landmarks[i] = f
				}
			}
		}

		// 解析aligned状态
		if aligned, ok := faceMap["aligned"].(bool); ok {
			face.Aligned = aligned
		}

		faces = append(faces, face)
	}

	return faces, nil
}

// parseEmbeddingsResponse 解析嵌入向量响应
func parseEmbeddingsResponse(results map[string]interface{}) ([]FaceEmbedding, error) {
	embeddingsData, ok := results["embeddings"]
	if !ok {
		return []FaceEmbedding{}, nil
	}

	embeddingsArray, ok := embeddingsData.([]interface{})
	if !ok {
		return nil, fmt.Errorf("embeddings field is not an array")
	}

	var embeddings []FaceEmbedding
	for _, embeddingData := range embeddingsArray {
		embeddingMap, ok := embeddingData.(map[string]interface{})
		if !ok {
			continue
		}

		var embedding FaceEmbedding

		// 解析face_id
		if faceID, ok := embeddingMap["face_id"].(float64); ok {
			embedding.FaceID = int(faceID)
		}

		// 解析embedding
		if embeddingVector, ok := embeddingMap["embedding"].([]interface{}); ok {
			embedding.Embedding = make([]float64, len(embeddingVector))
			for i, v := range embeddingVector {
				if f, ok := v.(float64); ok {
					embedding.Embedding[i] = f
				}
			}
		}

		// 解析bbox
		if bboxData, ok := embeddingMap["bbox"].([]interface{}); ok && len(bboxData) == 4 {
			embedding.BBox = make([]int, 4)
			for i, v := range bboxData {
				if f, ok := v.(float64); ok {
					embedding.BBox[i] = int(f)
				}
			}
		}

		// 解析confidence
		if conf, ok := embeddingMap["confidence"].(float64); ok {
			embedding.Confidence = conf
		}

		embeddings = append(embeddings, embedding)
	}

	return embeddings, nil
}
