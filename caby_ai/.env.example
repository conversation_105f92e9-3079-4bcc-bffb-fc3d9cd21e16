# =====================================
# 🤖 Caby AI - 视频分析服务环境变量配置
# =====================================
#
# 这是Caby AI项目的环境变量配置示例文件
# 复制此文件为 .env 并根据你的环境修改相应的值
#
# 使用方法：
# 1. cp .env.example .env
# 2. 编辑 .env 文件中的配置项
# 3. 运行服务
#

# =====================================
# 🚀 核心服务配置
# =====================================

# === 服务认证 ===
# 服务间通信认证令牌
CABY_AI_SERVICE_TOKEN=caby-token

# === Qdrant向量数据库配置 ===
# Qdrant服务地址
QDRANT_HOST=qdrant:6333
QDRANT_API_KEY=

# === Vision服务配置 ===
# Vision服务地址
VISION_HOST=caby_vision
VISION_API_KEY=default_api_key

# === Backend服务配置 ===
# Backend服务URL和认证令牌
BACKEND_SERVER_URL=https://api.caby.care
BACKEND_SERVICE_TOKEN=your_backend_service_token

# =====================================
# 🎯 影子模式配置
# =====================================

# 影子模式开关
SHADOW_MODE_ENABLED=true

# 相似度阈值 - 高于此值认为是已知猫咪
SHADOW_SIMILARITY_THRESHOLD=0.85

# 新猫阈值 - 低于此值认为是新猫咪
SHADOW_NEW_CAT_THRESHOLD=0.70

# 返回相似结果的数量
SHADOW_TOP_K=5

# =====================================
# 🧠 向量存储与学习配置
# =====================================

# 是否启用特征向量存储（用于持续学习）
# true: 每次识别都会存储特征向量到Qdrant，系统会持续学习
# false: 不存储特征向量，系统不会学习新特征
SHADOW_STORE_FEATURES=true

# 特征存储阈值 - 立即降低风险配置
# 0.0: 存储所有特征（最大学习效果，但包含噪声）
# 0.5: 存储高质量特征（立即实施，降低噪声风险）
# 0.6: 只存储优质特征（保守，学习较慢）
SHADOW_STORE_THRESHOLD=0.5

# 每只猫最多存储的特征数量
# 考虑Qdrant对比性能：特征数量直接影响搜索速度
# 200: 轻量级配置，搜索快速（~30ms）
# 300: 平衡配置，性能与效果兼顾（~50ms）
# 500: 高精度配置，搜索较慢（~80ms）
# 1000+: 高资源消耗，搜索很慢（~150ms+）
SHADOW_MAX_FEATURES_PER_CAT=250

# 特征演化策略
# time_based: 基于时间的演化，保留最新特征
# quality_based: 基于质量的演化，保留高质量特征
# hybrid: 混合策略，综合考虑时间和质量
SHADOW_EVOLUTION_STRATEGY=hybrid

# 特征演化周期（小时）
# ⚠️ 重要：考虑医疗场景（手术、戴圈）和季节性变化
# 168: 每周演化（风险：可能删除重要的正常状态特征）
# 336: 每2周演化（立即实施：减少医疗场景风险）
# 720: 每月演化（保守：适合有医疗或季节性变化的环境）
SHADOW_EVOLUTION_CYCLE=336

# =====================================
# 🛡️ 特征保护机制配置（新增）
# =====================================

# 是否启用特征保护机制
# true: 启用保护机制，防止重要特征被删除
# false: 禁用保护机制，所有特征平等对待
SHADOW_FEATURE_PROTECTION=true

# 保护最近N天的高质量特征
# 7: 保护最近一周的特征（推荐）
# 14: 保护最近两周的特征（保守）
# 30: 保护最近一个月的特征（超保守）
SHADOW_RECENT_DAYS_PROTECTION=7

# 保护比例 - 保护最近N天内质量最高的百分比特征
# 20: 保护20%的高质量特征（平衡）
# 30: 保护30%的高质量特征（保守）
# 10: 保护10%的高质量特征（激进）
SHADOW_PROTECTION_PERCENTAGE=20

# 时间衰减因子（天）
# 控制旧特征权重的衰减速度
# 7: 一周内特征权重显著衰减（快速适应）
# 30: 一个月内特征权重逐渐衰减（推荐）
# 90: 三个月内特征权重缓慢衰减（保守策略）
SHADOW_TIME_DECAY_FACTOR=30.0

# 质量权重比例
# 在特征演化中质量因素的权重占比
# 0.5: 质量和时间因素各占50%
# 0.7: 质量因素占70%，时间因素占30%（推荐）
# 0.9: 主要基于质量进行演化
SHADOW_QUALITY_WEIGHT_RATIO=0.7

# =====================================
# 🔧 高级配置
# =====================================

# 服务监听地址
CABY_AI_LISTEN_ADDR=:8765

# 最大并发处理数
MAX_CONCURRENCY=2

# 请求超时时间（秒）
REQUEST_TIMEOUT=30

# =====================================
# 🐳 Docker部署配置
# =====================================

# Docker网络配置
DOCKER_NETWORK=caby_network

# 日志级别
LOG_LEVEL=info

# =====================================
# 📊 监控和调试
# =====================================

# 启用调试模式
DEBUG_MODE=false

# 性能监控
ENABLE_METRICS=true

# 日志输出格式 (json|text)
LOG_FORMAT=text
