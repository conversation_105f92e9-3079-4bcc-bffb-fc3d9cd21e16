# Dockerfile for caby_ai service using gocv base image

# --- Builder Stage ---
# Use gocv's image which includes OpenCV 4.11.0 dev libraries
FROM gocv/opencv:4.11.0 AS builder

# Set Go version (matching go.mod toolchain)
ENV GO_VERSION=1.23.5

# Install build dependencies: Go, build tools, pkg-config
# gocv/opencv:4.11.0 is Ubuntu-based
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    pkg-config \
    wget \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Download and install Go (version updated to match go.mod)
RUN wget https://golang.org/dl/go${GO_VERSION}.linux-amd64.tar.gz && \
    tar -C /usr/local -xzf go${GO_VERSION}.linux-amd64.tar.gz && \
    rm go${GO_VERSION}.linux-amd64.tar.gz
ENV PATH="/usr/local/go/bin:${PATH}"

WORKDIR /app

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
# Set GOPROXY if needed, e.g., RUN export GOPROXY=https://goproxy.cn,direct && go mod download
# Ensure pkg-config can find OpenCV libs provided by the base image
# The base image should set PKG_CONFIG_PATH, but uncomment below if needed
# ENV PKG_CONFIG_PATH=/usr/local/lib/pkgconfig
RUN go mod download

# Copy the source code
COPY . .

# Build the application with CGO enabled (required for gocv)
# Build environment now matches runtime OpenCV version and go.mod toolchain
RUN CGO_ENABLED=1 go build -ldflags="-s -w" -o caby_ai ./main.go

# --- Final Stage ---
# Use the same gocv image for runtime consistency
FROM gocv/opencv:4.11.0

# Install only necessary *additional* runtime dependencies (e.g., for HTTPS)
# OpenCV libs are provided by the base image.
# ca-certificates might already be present from the base or builder install,
# but installing explicitly ensures it's there.
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /root/

# Copy the pre-built binary file from the builder stage
COPY --from=builder /app/caby_ai .

# Copy the configuration file
# Consider mounting config as a volume in production (as done in docker-compose.yml)
COPY config/config.yaml ./config/config.yaml

# Expose port (must match config.yaml)
EXPOSE 8765

# Command to run the executable
# Run as non-root user for better security (Optional but recommended)
# RUN useradd -ms /bin/bash appuser
# USER appuser
CMD ["./caby_ai"]
