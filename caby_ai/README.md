# 🤖 Caby AI - 智能视频分析与猫咪识别服务

基于深度学习的智能视频分析服务，专注于猫咪个体识别和行为分析，为智能猫厕所系统提供AI支持。

## 📋 项目概述

Caby AI是一个现代化的智能视频分析服务，采用微服务架构，提供高性能的视频分析、猫咪个体识别和行为检测功能。

### 🎯 核心特性

- ✅ **智能视频分析** - 自动提取关键帧并分析视频内容
- ✅ **猫咪个体识别** - 集成caby_vision进行高精度猫咪识别
- ✅ **静态视频检测** - 自动识别并跳过静态视频内容
- ✅ **缩略图分析** - 从backend_server获取缩略图进行分析
- ✅ **行为分析** - 检测猫咪行为模式和异常情况
- ✅ **服务鉴权** - 完整的Bearer Token认证体系
- ✅ **容器化部署** - Docker Compose一键部署
- ✅ **向量存储** - 集成Qdrant向量数据库

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Backend Server  │───▶│   Caby AI       │───▶│  Caby Vision    │
│  (端口: 5678)   │    │  (端口: 8765)   │    │  (端口: 8001)   │
│                 │    │                 │    │                 │
│ • 缩略图API     │    │ • 视频分析      │    │ • 猫咪识别      │
│ • 记录管理      │    │ • 静态检测      │    │ • 置信度评分    │
│ • 用户认证      │    │ • 行为分析      │    │ • 类别概率      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MinIO 存储    │    │  Qdrant 向量库  │    │   Reid 模型     │
│  (缩略图/视频)   │    │  (分析结果)     │    │ (猫咪识别模型)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 🔄 工作流程

1. **Backend Server** 接收视频记录请求并触发AI分析
2. **Caby AI** 接收分析请求，提取视频关键帧
3. **静态检测** - 分析视频是否为静态内容，如果是则直接返回
4. **缩略图获取** - 从 **Backend Server** 获取视频缩略图
5. **猫咪识别** - 将缩略图发送到 **Caby Vision** 进行个体识别
6. **名称映射** - 将识别的猫咪名称映射到对应的猫咪ID
7. **结果返回** - 返回完整的分析结果给 **Backend Server**
8. **数据存储** - 分析结果存储到 **Qdrant** 向量数据库

### 🎯 猫咪识别映射

当前版本支持的猫咪个体识别映射关系：

| 猫咪名称 | 猫咪ID | 描述 |
|---------|--------|------|
| 小黑 | f3ce1b02b2c1d755421000 | 黑色猫咪个体 |
| 小白 | f3ce1b02b40e9477c21000 | 白色猫咪个体 |
| 小花 | f3ce1b02b40ed223821000 | 花色猫咪个体 |

> 注意：这是第一版本的临时映射关系，后续版本将支持动态猫咪注册和管理。

### 🗂️ 目录结构

```
caby_ai/
├── api/                     # Go API层
│   ├── router.go           # 路由配置
│   ├── vision_handler.go   # 猫咪检测API处理器
│   └── analysis_handler.go # 视频分析处理器
├── pkg/                    # 核心业务逻辑
│   ├── analysis/           # 视频分析服务
│   ├── models/             # 数据模型定义
│   ├── qdrant/             # Qdrant客户端
│   └── auth/               # 认证中间件
├── config/                 # 配置文件
│   └── config.yaml         # 主配置文件
├── scripts/                # 工具脚本
│   ├── quick_deploy.sh     # 快速部署脚本
│   ├── logs.sh             # 日志查看脚本
│   └── test.py             # API测试脚本
├── config/                 # 配置文件
│   └── config.yaml         # 主配置文件
├── scripts/                # 工具脚本
│   ├── quick_deploy.sh     # 快速部署脚本
│   ├── test_face_api.py    # API测试脚本
│   └── logs.sh             # 日志查看脚本
├── docker-compose.yml      # Docker编排文件
├── Dockerfile.litserve     # LitServe容器构建文件
├── Dockerfile.cabyai       # Go API容器构建文件
└── README.md               # 项目文档
```

## 🚀 快速开始

### 先决条件

- Docker 20.10+
- Docker Compose 2.0+
- 至少2GB可用内存

### 一键部署

```bash
# 克隆项目
git clone <repository-url>
cd caby_ai

# 配置环境变量（可选）
cp env.example .env
# 编辑 .env 文件设置你的配置

# 一键部署
./scripts/quick_deploy.sh
```

### 手动部署

```bash
# 构建并启动所有服务
docker-compose up --build -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 环境变量配置

创建`.env`文件，参考`env.example`：

```bash
# 核心鉴权配置
CABY_AI_SERVICE_TOKEN=caby-token
QDRANT_API_KEY=test-secret-key
LITSERVE_API_KEY=default_api_key

# 服务端口配置
CABY_AI_PORT=8765
LITSERVE_PORT=8001

# 模型配置
FACE_MODEL_PATH=/app/models/face/anyface_v5.pt
LANDMARKS_PATH=/app/models/face/cat.npy
```

## 📡 API接口

### 认证方式

所有API请求需要在请求头中包含Bearer Token：

```bash
Authorization: Bearer caby-token
```

### 1. 猫咪检测API

#### 端点
```
POST /api/v1/vision/detect/cat
```

#### 请求参数
- **Content-Type**: `multipart/form-data`
- **image**: 图像文件 (必需)
- **task**: `"predict"` (默认: `"predict"`)
- **return_features**: 是否返回特征向量 (默认: `false`)
- **return_confidence**: 是否返回置信度 (默认: `true`)

#### 响应示例

**猫咪识别:**
```json
{
  "success": true,
  "predicted_cat": "小黑",
  "confidence": 0.8756,
  "class_probabilities": {
    "小黑": 0.8756,
    "小白": 0.1123,
    "小花": 0.0121
  },
  "process_time": 25.3,
  "task": "predict",
  "message": "Cat detection completed, predicted: 小黑 (confidence: 0.8756)",
  "request_id": "cat_req_1749643285643853380"
}
```

### 2. 视频分析API

#### 端点
```
POST /api/v1/analyze
```

#### 功能说明
这是Caby AI的核心API，集成了视频分析、静态检测、缩略图获取和猫咪识别的完整流程：

1. **视频分析** - 提取HLS视频流的关键帧
2. **静态检测** - 自动识别静态视频内容
3. **缩略图获取** - 从Backend Server获取视频缩略图
4. **猫咪识别** - 调用Caby Vision进行个体识别
5. **结果映射** - 将猫咪名称映射为对应的ID

#### 请求参数
- **Content-Type**: `application/json`

```json
{
  "video_id": "video_123456",
  "device_id": "device_001",
  "user_id": "user_001",
  "start_time": "2025-01-01T10:00:00Z",
  "end_time": "2025-01-01T10:05:00Z",
  "video_path": "https://example.com/videos/video_123456.m3u8",
  "known_cat_ids": ["f3ce1b02b2c1d755421000", "f3ce1b02b40e9477c21000"]
}
```

#### 响应示例

**正常视频分析结果:**
```json
{
  "video_id": "video_123456",
  "animal_id": "f3ce1b02b2c1d755421000",
  "cat_confidence": 0.8756,
  "behavior_type": "normal_poop",
  "is_abnormal": false,
  "abnormal_type": "",
  "abnormal_prob": 0.05,
  "ai_results": "{\"keyframes\":15,\"original_duration\":30.5,\"predicted_cat\":\"小黑\",\"confidence\":0.8756,\"class_probabilities\":{\"小黑\":0.8756,\"小白\":0.1123,\"小花\":0.0121}}",
  "created_at": "2025-01-01T10:00:00Z",
  "updated_at": "2025-01-01T10:00:30Z"
}
```

**静态视频检测结果:**
```json
{
  "video_id": "video_123456",
  "animal_id": "unknown",
  "cat_confidence": 0.0,
  "behavior_type": "static_video",
  "is_abnormal": false,
  "abnormal_type": "",
  "abnormal_prob": 0.0,
  "ai_results": "{\"status\":\"skipped\",\"reason\":\"static video\",\"original_duration\":45.2,\"keyframes\":2}",
  "created_at": "2025-01-01T10:00:00Z",
  "updated_at": "2025-01-01T10:00:15Z"
}
```

**特征提取 (task=embeddings):**
```json
{
  "success": true,
  "process_time": 52.1,
  "task": "embeddings", 
  "message": "Face embeddings extracted for 1 faces",
  "face_count": 1,
  "embeddings": [
    {
      "face_id": 0,
      "embedding": [0.1, 0.2, ..., 0.9], // 512维特征向量
      "bbox": [100, 150, 200, 250],
      "confidence": 0.95
    }
  ],
  "has_faces": true
}
```

### 3. 健康检查API

#### 主服务健康检查
```bash
GET /health
# 响应: {"status":"ok"}
```

#### Vision服务健康检查
```bash
GET /api/v1/vision/health
Authorization: Bearer <token>

# 响应:
{
  "service": "vision",
  "status": "healthy",
  "url": "http://caby_vision:8001"
}
```

## 🧪 测试

### 使用测试脚本

```bash
# 运行完整API测试
python scripts/test.py

# 运行特定测试
python scripts/test.py health vision_health cat_detection analyze

# 使用自定义图片测试
python scripts/test.py cat_detection --image /path/to/cat.jpg

# 详细输出
python scripts/test.py --verbose

# 输出示例:
# 🤖 Caby AI 综合测试
# ============================================================
# ✅ 主服务健康检查通过
# ✅ Vision健康检查通过
# ✅ 猫咪检测成功 (25.3ms)
#    预测猫咪: 小黑
#    置信度: 0.8756
# ✅ 视频分析成功
#    视频ID: test_video_001
#    识别的猫咪ID: f3ce1b02b2c1d755421000
#    行为类型: normal_poop
#    猫咪识别置信度: 0.8756
# 🎉 所有测试通过! Caby AI 视频分析和猫咪识别API运行正常!
```

### 测试覆盖范围

- ✅ **健康检查** - 验证主服务状态
- ✅ **Vision健康检查** - 验证Caby Vision集成
- ✅ **猫咪检测** - 测试图像上传和个体识别
- ✅ **视频分析** - 测试完整的视频分析流程
- ✅ **静态检测** - 验证静态视频识别功能
- ✅ **缩略图集成** - 测试与Backend Server的集成
- ✅ **名称映射** - 验证猫咪名称到ID的映射

### 使用cURL测试

```bash
# 猫咪检测
curl -X POST \
  -H "Authorization: Bearer caby-token" \
  -F "image=@cat_image.jpg" \
  -F "task=predict" \
  -F "return_confidence=true" \
  http://localhost:8765/api/v1/vision/detect/cat

# 视频分析
curl -X POST \
  -H "Authorization: Bearer caby-token" \
  -H "Content-Type: application/json" \
  -d '{
    "video_id": "test_video_001",
    "device_id": "device_001",
    "user_id": "user_001",
    "start_time": "2025-01-01T10:00:00Z",
    "end_time": "2025-01-01T10:05:00Z",
    "video_path": "https://example.com/videos/test.m3u8",
    "known_cat_ids": ["f3ce1b02b2c1d755421000"]
  }' \
  http://localhost:8765/api/v1/analyze

# 健康检查
curl -H "Authorization: Bearer caby-token" \
  http://localhost:8765/api/v1/vision/health
```

## 🔗 服务集成

### Backend Server 集成

Caby AI与Backend Server紧密集成，提供完整的视频分析服务：

#### 缩略图获取
```bash
# Caby AI从Backend Server获取缩略图
GET /api/records/videos/thumbnail/{folder}?bucket={bucket}
Authorization: Bearer {service_token}
```

#### 分析触发流程
1. Backend Server接收客户端的分析请求
2. Backend Server调用Caby AI的 `/api/v1/analyze` 端点
3. Caby AI处理视频并返回分析结果
4. Backend Server存储结果并通知客户端

### Caby Vision 集成

Caby AI调用Caby Vision进行猫咪个体识别：

#### 识别请求
```bash
# Caby AI向Caby Vision发送识别请求
POST http://caby_vision:8001/predict
Content-Type: application/json
Authorization: Bearer {vision_api_key}

{
  "image": "base64_encoded_image",
  "return_features": false,
  "return_confidence": true,
  "task": "predict"
}
```

#### 响应处理
- 接收猫咪名称和置信度
- 映射名称到对应的猫咪ID
- 整合到最终分析结果中

### 服务依赖关系

```
Backend Server (5678) ──┐
                        ├──▶ Caby AI (8765) ──▶ Caby Vision (8001)
Client App ─────────────┘                   └──▶ Qdrant (6333)
```

### 配置要求

确保以下配置正确设置：

```yaml
# caby_ai/config/config.yaml
backend_server_url: "http://backend_server:5678"
vision:
  host: "caby_vision"
  port: 8001
  api_key: "your_vision_api_key"
auth:
  service_token: "your_service_token"
```

## 🔧 运维指南

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f litserve
docker-compose logs -f caby_ai
docker-compose logs -f qdrant

# 使用脚本查看日志
./scripts/logs.sh litserve -n 100
```

### 服务管理

```bash
# 重启服务
docker-compose restart

# 重启特定服务
docker-compose restart litserve

# 停止服务
docker-compose down

# 重新构建并启动
docker-compose up --build -d

# 仅重启不重新构建
./scripts/quick_deploy.sh --skip-build
```

### 监控和调试

```bash
# 检查容器状态
docker-compose ps

# 检查资源使用
docker stats

# 进入容器调试
docker-compose exec litserve bash
docker-compose exec caby_ai sh
```

## 📊 性能指标

### 处理性能

| 操作 | CPU耗时 | GPU耗时 | 内存占用 |
|-----|--------|--------|---------|
| 人脸检测 | ~30-50ms | ~10-15ms | ~200MB |
| 特征提取 | ~50-70ms | ~15-25ms | ~220MB |
| 人脸对齐 | ~5-10ms | ~2-5ms | +10MB |

### 系统资源

- **总内存占用**: ~300MB (不含系统)
- **模型大小**: 93MB (anyface_v5.pt)
- **并发支持**: 1 worker (可配置)
- **支持格式**: JPEG, PNG, WebP, BMP

## 🛠️ 技术栈

- **后端**: Go 1.21+ (API网关)
- **ML服务**: Python 3.9+ (LitServe)
- **深度学习**: PyTorch 2.0+
- **计算机视觉**: OpenCV, scikit-image
- **向量数据库**: Qdrant 1.14+
- **容器化**: Docker + Docker Compose
- **HTTP框架**: Gin (Go), FastAPI (Python)

## 🔒 安全考虑

### 认证和授权

- 所有API端点都需要Bearer Token认证
- Token在配置文件中设置，支持环境变量覆盖
- 推荐在生产环境中使用强随机Token

### 网络安全

- 服务间通信在Docker内部网络中进行
- 只有必要的端口暴露到宿主机
- 支持CORS配置用于跨域请求

### 数据安全

- 图像数据仅在内存中处理，不持久化存储
- Qdrant向量数据库支持API Key认证
- 支持TLS/SSL（需要额外配置）

## 🚨 故障排查

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep 8765
   
   # 检查Docker资源
   docker system df
   docker system prune -f
   ```

2. **模型加载失败**
   ```bash
   # 检查模型文件
   ls -la models/face/
   
   # 检查LitServe日志
   docker-compose logs litserve | grep -i error
   ```

3. **API请求失败**
   ```bash
   # 检查鉴权配置
   echo $CABY_AI_SERVICE_TOKEN
   
   # 测试内部连接
   docker-compose exec caby_ai curl http://litserve:8001/health
   ```

### 日志分析

```bash
# 错误日志过滤
docker-compose logs | grep -i error

# 性能日志分析
docker-compose logs litserve | grep "process_time"

# 请求追踪
docker-compose logs | grep "request_id"
```

## 📈 扩展和自定义

### 添加新的检测模型

1. 在`litserve_service/face_detector.py`中扩展检测器
2. 更新模型路径配置
3. 在`face_server.py`中添加新的任务类型

### 集成其他服务

1. 在`api/router.go`中添加新的路由
2. 创建对应的处理器
3. 更新Docker Compose配置

### 性能优化

- 启用GPU加速：修改`device`配置为`cuda`
- 增加worker数量：调整`workers_per_device`参数
- 配置模型缓存：使用Redis等缓存服务

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交Issue和Pull Request！请参考 [CONTRIBUTING.md](CONTRIBUTING.md) 了解贡献指南。

## 📞 支持

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-org/caby-ai/issues)
- 📖 文档: [Wiki](https://github.com/your-org/caby-ai/wiki)

---

⭐ 如果这个项目对你有帮助，请给它一个Star！ 