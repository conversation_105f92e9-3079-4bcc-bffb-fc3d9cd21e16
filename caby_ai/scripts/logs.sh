#!/bin/bash

# Script to view logs from the caby-ai Docker container(s)

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Directory where docker-compose.yml is expected (parent directory)
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR" || { echo -e "${RED}Failed to change directory to $PROJECT_DIR${NC}"; exit 1; }

# Default values
SERVICE="" # Service name (empty means all)
LINES=100
FOLLOW=true

# Function to display usage information
show_usage() {
    echo -e "${GREEN}Caby AI Docker Logs Viewer${NC}"
    echo "Usage: $0 [options] [service]"
    echo
    echo "Options:"
    echo "  -n, --lines NUMBER   Number of lines to show (default: 100)"
    echo "  -f, --follow         Follow log output (default)"
    echo "      --no-follow      Do not follow log output (static)"
    echo "  -h, --help           Show this help message"
    echo
    echo "Services:"
    echo "  caby_ai              View logs for the main caby_ai service"
    echo "  litserve             View logs for the LitServe AI inference service"
    echo "  all                  View logs for all services (default)"
    echo
    echo "Example:"
    echo "  $0                   # View last 100 lines from all services and follow"
    echo "  $0 caby_ai           # View logs from caby_ai service only"
    echo "  $0 litserve          # View logs from litserve service only"
    echo "  $0 -n 50 --no-follow # View last 50 lines from all services without following"
}

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        -n|--lines)
            if [[ "$2" =~ ^[0-9]+$ ]]; then
                LINES="$2"
                shift 2
            else
                echo -e "${RED}Error: --lines requires a numeric argument.${NC}" >&2
                exit 1
            fi
            ;;
        -f|--follow)
            FOLLOW=true
            shift
            ;;
        --no-follow)
            FOLLOW=false
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        # Add known service names here
        caby_ai|litserve|all)
             # Check if SERVICE is already set (prevent multiple services)
            if [ -n "$SERVICE" ]; then
                echo -e "${RED}Error: Please specify only one service.${NC}"
                show_usage
                exit 1
            fi
            SERVICE="$1"
            shift
            ;;
        *)
            # Check if it's an unknown argument or potentially the service name
            # If SERVICE is empty, assume this is the service name
            if [ -z "$SERVICE" ] && [[ ! "$1" =~ ^- ]]; then
                 echo -e "${YELLOW}Warning: Unknown service '$1'. Attempting to use it directly.${NC}"
                 SERVICE="$1"
                 shift
            else
                echo -e "${RED}Unknown option or multiple services specified: $1${NC}"
                show_usage
                exit 1
            fi
            ;;
    esac
done

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
     echo -e "${RED}Error: docker-compose.yml not found in $PROJECT_DIR.${NC}"
     # Optional: Add fallback to 'docker logs' if needed, like in the caby_intelligence script
     exit 1
fi

echo -e "${YELLOW}Using docker-compose from $PROJECT_DIR${NC}"

# Determine target service(s)
TARGET_SERVICE=""
if [ -z "$SERVICE" ] || [ "$SERVICE" = "all" ]; then
    echo -e "${YELLOW}Target: All services${NC}"
    TARGET_SERVICE="" # docker-compose logs without service name targets all
else
    # Optional: Add mapping here if needed (like get_docker_service in backend)
    # For now, assume SERVICE name matches docker-compose service name
    echo -e "${YELLOW}Target: Service '${BLUE}$SERVICE${YELLOW}'${NC}"
    TARGET_SERVICE="$SERVICE"
fi


# Execute docker-compose logs command
echo -e "${YELLOW}Showing logs (last $LINES lines${NC}${YELLOW}${FOLLOW:+, follow mode})${NC}"

if [ "$FOLLOW" = true ]; then
    docker-compose logs -f --tail "$LINES" $TARGET_SERVICE
else
    docker-compose logs --tail "$LINES" $TARGET_SERVICE
fi
