#!/bin/bash

# Quick deployment script for caby_ai

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Directory where docker-compose.yml is located
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR" || { echo -e "${RED}Failed to change directory to $PROJECT_DIR${NC}"; exit 1; }

# Check for docker-compose
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: docker-compose is not installed.${NC}"
    echo "Please install docker-compose first."
    exit 1
fi

# Check for .env file
if [ ! -f "$PROJECT_DIR/.env" ]; then
    echo -e "${YELLOW}Warning: .env file not found.${NC}"
    read -p "Do you want to continue without an .env file? [y/N] " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Deployment canceled."
        exit 1
    fi
fi

# Default options
SKIP_BUILD=false

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --skip-build) SKIP_BUILD=true ;;
        -h|--help)
            echo "Usage: ./quick_deploy.sh [options]"
            echo "Options:"
            echo "  --skip-build    Skip the build step (restart only)"
            echo "  -h, --help      Show this help message"
            exit 0
            ;;
        *) echo "Unknown option: $1"; exit 1 ;;
    esac
    shift
done

echo -e "${GREEN}Starting deployment of caby_ai services...${NC}"
echo -e "${YELLOW}Working directory: $(pwd)${NC}"

# Step 1: Bring down any running containers
echo -e "${YELLOW}Stopping any running containers...${NC}"
docker-compose down
if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to stop containers.${NC}"
    exit 1
fi
echo -e "${GREEN}Successfully stopped containers.${NC}"

# Step 2: Build containers (unless skipped)
if [ "$SKIP_BUILD" = false ]; then
    echo -e "${YELLOW}Building Docker images...${NC}"
    docker-compose build
    if [ $? -ne 0 ]; then
        echo -e "${RED}Docker build failed. Please check Dockerfile and build context.${NC}"
        exit 1
    fi
    echo -e "${GREEN}Successfully built containers.${NC}"
    
    # Clean up dangling images to save disk space
    echo -e "${YELLOW}Cleaning up dangling images (<none>:<none>)...${NC}"
    dangling_images=$(docker images -f "dangling=true" -q)
    if [ ! -z "$dangling_images" ]; then
        docker image prune -f
        if [ $? -ne 0 ]; then
            echo -e "${YELLOW}Warning: Failed to clean up some dangling images.${NC}"
        else
            echo -e "${GREEN}Successfully cleaned up dangling images.${NC}"
        fi
    else
        echo -e "${GREEN}No dangling images found.${NC}"
    fi
else
    echo -e "${YELLOW}Skipping build step as requested (restart only).${NC}"
fi

# Step 3: Start containers in detached mode
echo -e "${YELLOW}Starting services in detached mode...${NC}"
docker-compose up -d
if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to start services. Please check docker-compose logs.${NC}"
    exit 1
fi

echo -e "${GREEN}Deployment completed successfully!${NC}"
echo -e "Services:"
echo -e "  - caby_ai: http://localhost:8765/health"
echo -e "  - qdrant: http://localhost:6333"

# Show container status
echo -e "\n${YELLOW}Container status:${NC}"
docker-compose ps

# Display container hashes for log inspection
echo -e "\n${YELLOW}Container hashes for log inspection:${NC}"
echo -e "${GREEN}Format: SERVICE_NAME: CONTAINER_HASH${NC}"

# Get running containers from this docker-compose project
for service in caby_ai qdrant; do
    container_id=$(docker-compose ps -q $service)
    if [ ! -z "$container_id" ]; then
        echo -e "${service}: ${container_id}"
        echo -e "  - Check logs with: ${GREEN}docker logs ${container_id}${NC}"
    else
        echo -e "${service}: ${RED}Not running${NC}"
    fi
done

echo -e "\n${GREEN}To check all logs, run: docker-compose logs -f${NC}"

# Add a small delay to allow services to initialize before potential tests
sleep 5

# Optional: Add test commands here, e.g., curl health check
echo -e "\n${YELLOW}Running basic health checks...${NC}"

# Check caby_ai health
curl -s http://localhost:8765/health | grep '{"status":"ok"}' > /dev/null
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ caby_ai health check PASSED${NC}"
else
    echo -e "${RED}✗ caby_ai health check FAILED${NC}"
fi

echo -e "\n${YELLOW}Available commands:${NC}"
echo -e "  - Face API test: python scripts/test_face_api.py"
echo -e "  - View logs: docker-compose logs -f"
echo -e "  - Check service status: docker-compose ps"
echo -e "  - Restart only: ./scripts/quick_deploy.sh --skip-build"

echo -e "\n${GREEN}Script finished.${NC}"
