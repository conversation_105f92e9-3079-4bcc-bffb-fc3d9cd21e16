package main

import (
	"context"
	"log"
	"os"

	"caby-ai/api"
	"caby-ai/config"
	"caby-ai/pkg/analysis"
	"caby-ai/pkg/qdrant"
)

func main() {
	// 获取配置文件路径，支持环境变量覆盖
	configFile := os.Getenv("CONFIG_FILE")
	if configFile == "" {
		configFile = "./config/config.yaml"
	}

	log.Printf("Loading config from: %s", configFile)

	cfg, err := config.LoadConfig(configFile)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	log.Printf("Qdrant config: host=%s, scheme=%s", cfg.Qdrant.Host, cfg.Qdrant.Scheme)

	// Initialize Qdrant client
	qdrantClient, err := qdrant.NewClient(cfg.Qdrant)
	if err != nil {
		log.Fatalf("Failed to initialize Qdrant client: %v", err)
	}

	// Ensure Qdrant collection exists
	if err := qdrantClient.EnsureCollection(context.Background()); err != nil {
		log.Fatalf("Failed to ensure Qdrant collection: %v", err)
	}

	// Initialize Analysis Service
	analysisService := analysis.NewService(cfg, qdrantClient)

	// Initialize Analysis Handler
	analysisHandler := api.NewAnalysisHandler(analysisService) // Create handler

	// Initialize Vision Handler
	visionHandler := api.NewVisionHandler(cfg)

	// Initialize Admin Handler (for cat initialization)
	adminHandler := api.NewAdminHandler(analysisService.GetShadowService())

	// Setup Router, passing the handlers
	router := api.SetupRouter(cfg, analysisHandler, visionHandler, adminHandler) // Pass handlers to router setup

	log.Printf("Starting caby_ai server on %s", cfg.Server.ListenAddr)
	if err := router.Run(cfg.Server.ListenAddr); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
