worker_processes  auto;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile on;
    tcp_nodelay on;
    tcp_nopush on;

    keepalive_timeout  65;
    types_hash_max_size 4096;
    types_hash_bucket_size 256;

    # Main site configuration
    server {
        listen       80;
        listen       [::]:80;
        server_name  caby.care www.caby.care;
        return 301 https://$host$request_uri;
    }

    server {
        listen       443 ssl;
        listen       [::]:443 ssl;
        server_name  caby.care www.caby.care;

        ssl_certificate /root/.cert/caby.care.pem;
        ssl_certificate_key /root/.cert/caby.care.key;

        access_log /var/log/nginx/caby.care.access.log;
        error_log /var/log/nginx/caby.care.error.log;

        # Adjust timeouts
        keepalive_timeout 75s;
        keepalive_requests 100;
        send_timeout 60s;

        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;

            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type";
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }
    }

    # Minio configuration
    server {
        listen       80;
        listen       [::]:80;
        server_name  minio.caby.care;
        return 301 https://$host$request_uri;
    }

    server {
        listen       443 ssl;
        listen       [::]:443 ssl;
        server_name  minio.caby.care;

        ssl_certificate /root/.cert/caby.care.pem;
        ssl_certificate_key /root/.cert/caby.care.key;

        access_log /var/log/nginx/minio.caby.care.access.log;
        error_log /var/log/nginx/minio.caby.care.error.log;

        # Minio proxy configuration
        location / {
            proxy_pass http://localhost:9001;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # WebSocket support
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

            # Properly handle WebSocket connection timeouts
            proxy_read_timeout 60s;
            proxy_send_timeout 60s;
            
            proxy_connect_timeout 300;
            proxy_http_version 1.1;
            chunked_transfer_encoding off;
            
            client_max_body_size 0;
        }
    }

    # API configuration
    server {
        listen       80;
        listen       [::]:80;
        server_name  api.caby.care;
        return 301 https://$host$request_uri;
    }

    server {
        listen       443 ssl;
        listen       [::]:443 ssl;
        server_name  api.caby.care;

        ssl_certificate /root/.cert/caby.care.pem;
        ssl_certificate_key /root/.cert/caby.care.key;

        access_log /var/log/nginx/api.caby.care.access.log;
        error_log /var/log/nginx/api.caby.care.error.log;

        location / {
            proxy_pass http://localhost:5678;
            proxy_http_version 1.1;
            proxy_redirect off;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Original-Scheme $scheme;
            proxy_set_header X-Forwarded-Ssl on;
            
            # CORS headers
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            
            # Handle OPTIONS method for CORS preflight
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }
        }
    }
}
