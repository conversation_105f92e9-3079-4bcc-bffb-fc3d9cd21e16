package service

import (
	"backup-server/internal/config"
	"backup-server/internal/model"
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// SyncService 同步服务
type SyncService struct {
	config          *config.Config
	videoService    *VideoService
	storageService  *StorageService
	downloadService *DownloadService
	ctx             context.Context
	cancel          context.CancelFunc
	syncMutex       sync.Mutex
	lastSyncTime    time.Time
	isRunning       bool
}

// NewSyncService 创建同步服务
func NewSyncService(cfg *config.Config, videoService *VideoService, storageService *StorageService, downloadService *DownloadService) *SyncService {
	ctx, cancel := context.WithCancel(context.Background())

	service := &SyncService{
		config:          cfg,
		videoService:    videoService,
		storageService:  storageService,
		downloadService: downloadService,
		ctx:             ctx,
		cancel:          cancel,
	}

	// 如果启用自动同步，启动定时同步
	if cfg.Sync.AutoSync {
		go service.startAutoSync()
	}

	return service
}

// startAutoSync 启动自动同步
func (s *SyncService) startAutoSync() {
	ticker := time.NewTicker(s.config.Sync.CheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := s.TriggerSync(); err != nil {
				log.Printf("自动同步失败: %v", err)
			}
		case <-s.ctx.Done():
			return
		}
	}
}

// TriggerSync 触发同步
func (s *SyncService) TriggerSync() error {
	s.syncMutex.Lock()
	defer s.syncMutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("同步正在进行中")
	}

	s.isRunning = true
	defer func() {
		s.isRunning = false
		s.lastSyncTime = time.Now()
	}()

	log.Println("开始同步...")

	// 获取云端视频列表
	cloudVideos, err := s.getCloudVideoList()
	if err != nil {
		return fmt.Errorf("获取云端视频列表失败: %v", err)
	}

	// 获取本地同步状态
	syncStatus, err := s.storageService.LoadSyncStatus()
	if err != nil {
		return fmt.Errorf("加载同步状态失败: %v", err)
	}

	// 找出需要下载的视频
	toDownload := s.findVideosToDownload(cloudVideos, syncStatus)

	log.Printf("发现 %d 个新视频需要下载", len(toDownload))

	// 批量创建下载任务
	if len(toDownload) > 0 {
		tasks, errors := s.downloadService.CreateBatchDownloadTasks(toDownload)
		if len(errors) > 0 {
			log.Printf("创建下载任务时出现 %d 个错误", len(errors))
			for _, err := range errors {
				log.Printf("错误: %v", err)
			}
		}
		log.Printf("成功创建 %d 个下载任务", len(tasks))
	}

	// 更新同步状态
	syncStatus.LastSync = time.Now()
	if err := s.storageService.SaveSyncStatus(syncStatus); err != nil {
		log.Printf("保存同步状态失败: %v", err)
	}

	log.Println("同步完成")
	return nil
}

// getCloudVideoList 获取云端视频列表
func (s *SyncService) getCloudVideoList() ([]model.VideoInfo, error) {
	var allVideos []model.VideoInfo
	page := 1
	limit := s.config.Sync.BatchSize

	for {
		filter := &model.VideoFilter{
			Page:  page,
			Limit: limit,
		}

		response, err := s.videoService.ListCloudVideos(filter)
		if err != nil {
			return nil, err
		}

		allVideos = append(allVideos, response.Videos...)

		// 如果返回的视频数量少于限制，说明已经获取完所有视频
		if len(response.Videos) < limit {
			break
		}

		page++
	}

	return allVideos, nil
}

// findVideosToDownload 找出需要下载的视频
func (s *SyncService) findVideosToDownload(cloudVideos []model.VideoInfo, syncStatus *model.SyncStatus) []string {
	var toDownload []string

	for _, video := range cloudVideos {
		// 检查是否已经下载
		if _, exists := syncStatus.DownloadedVideos[video.VideoID]; exists {
			continue
		}

		// 检查是否在失败列表中（可以重试）
		inFailedList := false
		for _, failed := range syncStatus.FailedDownloads {
			if failed.VideoID == video.VideoID {
				// 如果失败次数超过配置的重试次数，跳过
				if failed.Attempts >= s.config.Download.RetryAttempts {
					inFailedList = true
					break
				}
			}
		}

		if !inFailedList {
			toDownload = append(toDownload, video.VideoID)
		}
	}

	return toDownload
}

// GetSyncStatus 获取同步状态
func (s *SyncService) GetSyncStatus() (map[string]interface{}, error) {
	syncStatus, err := s.storageService.LoadSyncStatus()
	if err != nil {
		return nil, err
	}

	// 获取存储统计
	storageStats, err := s.storageService.GetStorageStats()
	if err != nil {
		return nil, err
	}

	// 获取任务状态
	taskStatus := s.downloadService.GetTaskStatus()

	return map[string]interface{}{
		"last_sync":        syncStatus.LastSync,
		"is_running":       s.isRunning,
		"last_sync_time":   s.lastSyncTime,
		"downloaded_count": len(syncStatus.DownloadedVideos),
		"failed_count":     len(syncStatus.FailedDownloads),
		"pending_count":    len(syncStatus.PendingDownloads),
		"storage_stats":    storageStats,
		"task_status":      taskStatus,
	}, nil
}

// GetSyncDiff 获取同步差异
func (s *SyncService) GetSyncDiff() (map[string]interface{}, error) {
	// 获取云端视频列表
	cloudVideos, err := s.getCloudVideoList()
	if err != nil {
		return nil, fmt.Errorf("获取云端视频列表失败: %v", err)
	}

	// 获取本地同步状态
	syncStatus, err := s.storageService.LoadSyncStatus()
	if err != nil {
		return nil, fmt.Errorf("加载同步状态失败: %v", err)
	}

	// 分析差异
	cloudVideoMap := make(map[string]model.VideoInfo)
	for _, video := range cloudVideos {
		cloudVideoMap[video.VideoID] = video
	}

	var cloudOnly []model.VideoInfo    // 云端有，本地没有
	var localOnly []string             // 本地有，云端没有
	var both []string                  // 两边都有

	// 找出云端独有的视频
	for videoID, video := range cloudVideoMap {
		if _, exists := syncStatus.DownloadedVideos[videoID]; !exists {
			cloudOnly = append(cloudOnly, video)
		} else {
			both = append(both, videoID)
		}
	}

	// 找出本地独有的视频
	for videoID := range syncStatus.DownloadedVideos {
		if _, exists := cloudVideoMap[videoID]; !exists {
			localOnly = append(localOnly, videoID)
		}
	}

	return map[string]interface{}{
		"cloud_total":  len(cloudVideos),
		"local_total":  len(syncStatus.DownloadedVideos),
		"cloud_only":   cloudOnly,
		"local_only":   localOnly,
		"both":         both,
		"need_download": len(cloudOnly),
		"need_cleanup":  len(localOnly),
	}, nil
}

// CleanupOrphanedVideos 清理孤立的视频（云端已删除但本地还存在）
func (s *SyncService) CleanupOrphanedVideos() error {
	diff, err := s.GetSyncDiff()
	if err != nil {
		return err
	}

	localOnly, ok := diff["local_only"].([]string)
	if !ok || len(localOnly) == 0 {
		return nil
	}

	log.Printf("发现 %d 个孤立视频，开始清理...", len(localOnly))

	var cleanupErrors []error
	for _, videoID := range localOnly {
		if err := s.storageService.DeleteLocalVideo(videoID); err != nil {
			cleanupErrors = append(cleanupErrors, fmt.Errorf("删除视频失败 %s: %v", videoID, err))
		} else {
			log.Printf("已删除孤立视频: %s", videoID)
		}
	}

	if len(cleanupErrors) > 0 {
		return fmt.Errorf("清理过程中出现 %d 个错误", len(cleanupErrors))
	}

	log.Printf("成功清理 %d 个孤立视频", len(localOnly))
	return nil
}

// Stop 停止同步服务
func (s *SyncService) Stop() {
	s.cancel()
}
