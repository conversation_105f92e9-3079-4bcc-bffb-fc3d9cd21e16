package service

import (
	"backup-server/internal/config"
	"backup-server/internal/model"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"time"
)

// StorageService 本地存储服务
type StorageService struct {
	config *config.Config
}

// NewStorageService 创建存储服务
func NewStorageService(cfg *config.Config) *StorageService {
	return &StorageService{
		config: cfg,
	}
}

// InitStorage 初始化存储目录
func (s *StorageService) InitStorage() error {
	dirs := []string{
		s.config.Storage.DataDir,
		s.config.Storage.VideosDir,
		s.config.Storage.MetadataDir,
		filepath.Join(s.config.Storage.VideosDir, "by_cat"),
		filepath.Join(s.config.Storage.VideosDir, "by_device"),
		filepath.Dir(s.config.Log.File),
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录失败 %s: %v", dir, err)
		}
	}

	return nil
}

// LoadSyncStatus 加载同步状态
func (s *StorageService) LoadSyncStatus() (*model.SyncStatus, error) {
	if _, err := os.Stat(s.config.Storage.SyncFile); os.IsNotExist(err) {
		// 如果文件不存在，返回默认状态
		return &model.SyncStatus{
			LastSync:         time.Time{},
			DownloadedVideos: make(map[string]model.LocalVideoInfo),
			FailedDownloads:  []model.FailedDownload{},
			PendingDownloads: []string{},
		}, nil
	}

	data, err := os.ReadFile(s.config.Storage.SyncFile)
	if err != nil {
		return nil, fmt.Errorf("读取同步状态文件失败: %v", err)
	}

	var status model.SyncStatus
	if err := json.Unmarshal(data, &status); err != nil {
		return nil, fmt.Errorf("解析同步状态失败: %v", err)
	}

	return &status, nil
}

// SaveSyncStatus 保存同步状态
func (s *StorageService) SaveSyncStatus(status *model.SyncStatus) error {
	data, err := json.MarshalIndent(status, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化同步状态失败: %v", err)
	}

	if err := os.WriteFile(s.config.Storage.SyncFile, data, 0644); err != nil {
		return fmt.Errorf("保存同步状态文件失败: %v", err)
	}

	return nil
}

// GetVideoPath 获取视频存储路径
func (s *StorageService) GetVideoPath(viewMode model.ViewMode, deviceID, catID string, startTime int64) string {
	timestamp := time.Unix(startTime, 0)
	folderName := timestamp.Format("2006-01-02_15-04-05")

	switch viewMode {
	case model.ViewModeByCat:
		return filepath.Join(s.config.Storage.VideosDir, "by_cat", catID, folderName)
	case model.ViewModeByDevice:
		return filepath.Join(s.config.Storage.VideosDir, "by_device", deviceID, folderName)
	default:
		return filepath.Join(s.config.Storage.VideosDir, "by_device", deviceID, folderName)
	}
}

// ListLocalVideos 列出本地视频
func (s *StorageService) ListLocalVideos(req *model.LocalVideoListRequest) (*model.LocalVideoListResponse, error) {
	status, err := s.LoadSyncStatus()
	if err != nil {
		return nil, err
	}

	var videos []model.LocalVideoInfo
	for _, video := range status.DownloadedVideos {
		// 根据查看模式和筛选条件过滤
		if req.CatID != nil && video.CatID != *req.CatID {
			continue
		}
		if req.DeviceID != nil && video.DeviceID != *req.DeviceID {
			continue
		}

		videos = append(videos, video)
	}

	// 按时间排序
	sort.Slice(videos, func(i, j int) bool {
		return videos[i].StartTime > videos[j].StartTime
	})

	// 分页
	total := int64(len(videos))
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}

	start := (req.Page - 1) * req.Limit
	end := start + req.Limit
	if start >= len(videos) {
		videos = []model.LocalVideoInfo{}
	} else if end > len(videos) {
		videos = videos[start:]
	} else {
		videos = videos[start:end]
	}

	return &model.LocalVideoListResponse{
		Videos:   videos,
		Total:    total,
		Page:     req.Page,
		Limit:    req.Limit,
		ViewMode: req.ViewMode,
	}, nil
}

// GetLocalVideoInfo 获取本地视频信息
func (s *StorageService) GetLocalVideoInfo(videoID string) (*model.LocalVideoInfo, error) {
	status, err := s.LoadSyncStatus()
	if err != nil {
		return nil, err
	}

	video, exists := status.DownloadedVideos[videoID]
	if !exists {
		return nil, fmt.Errorf("视频不存在: %s", videoID)
	}

	return &video, nil
}

// DeleteLocalVideo 删除本地视频
func (s *StorageService) DeleteLocalVideo(videoID string) error {
	status, err := s.LoadSyncStatus()
	if err != nil {
		return err
	}

	video, exists := status.DownloadedVideos[videoID]
	if !exists {
		return fmt.Errorf("视频不存在: %s", videoID)
	}

	// 删除文件
	if err := os.RemoveAll(video.LocalPath); err != nil {
		return fmt.Errorf("删除视频文件失败: %v", err)
	}

	// 从同步状态中移除
	delete(status.DownloadedVideos, videoID)

	// 保存状态
	return s.SaveSyncStatus(status)
}

// GetStorageStats 获取存储统计信息
func (s *StorageService) GetStorageStats() (map[string]interface{}, error) {
	status, err := s.LoadSyncStatus()
	if err != nil {
		return nil, err
	}

	var totalSize int64
	var totalFiles int
	deviceStats := make(map[string]int)
	catStats := make(map[string]int)

	for _, video := range status.DownloadedVideos {
		totalSize += video.TotalSize
		totalFiles += video.FileCount
		deviceStats[video.DeviceID]++
		catStats[video.CatID]++
	}

	return map[string]interface{}{
		"total_videos": len(status.DownloadedVideos),
		"total_size":   totalSize,
		"total_files":  totalFiles,
		"device_stats": deviceStats,
		"cat_stats":    catStats,
		"failed_downloads": len(status.FailedDownloads),
		"pending_downloads": len(status.PendingDownloads),
	}, nil
}

// IsVideoDownloaded 检查视频是否已下载
func (s *StorageService) IsVideoDownloaded(videoID string) bool {
	status, err := s.LoadSyncStatus()
	if err != nil {
		return false
	}

	_, exists := status.DownloadedVideos[videoID]
	return exists
}

// GetCatList 获取猫咪列表
func (s *StorageService) GetCatList() ([]string, error) {
	status, err := s.LoadSyncStatus()
	if err != nil {
		return nil, err
	}

	catSet := make(map[string]bool)
	for _, video := range status.DownloadedVideos {
		if video.CatID != "" {
			catSet[video.CatID] = true
		}
	}

	var cats []string
	for cat := range catSet {
		cats = append(cats, cat)
	}

	sort.Strings(cats)
	return cats, nil
}

// GetDeviceList 获取设备列表
func (s *StorageService) GetDeviceList() ([]string, error) {
	status, err := s.LoadSyncStatus()
	if err != nil {
		return nil, err
	}

	deviceSet := make(map[string]bool)
	for _, video := range status.DownloadedVideos {
		if video.DeviceID != "" {
			deviceSet[video.DeviceID] = true
		}
	}

	var devices []string
	for device := range deviceSet {
		devices = append(devices, device)
	}

	sort.Strings(devices)
	return devices, nil
}
