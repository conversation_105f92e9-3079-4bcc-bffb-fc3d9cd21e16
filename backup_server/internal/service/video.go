package service

import (
	"backup-server/internal/config"
	"backup-server/internal/model"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
)

// VideoService 视频服务
type VideoService struct {
	config     *config.Config
	httpClient *http.Client
}

// NewVideoService 创建视频服务
func NewVideoService(cfg *config.Config) *VideoService {
	// 创建HTTP客户端，支持代理
	client := &http.Client{
		Timeout: cfg.CloudAPI.Timeout,
	}

	// 如果启用代理，设置代理
	if cfg.Proxy.Enabled {
		if proxyURL, err := url.Parse(cfg.Proxy.HTTPProxy); err == nil {
			client.Transport = &http.Transport{
				Proxy: http.ProxyURL(proxyURL),
			}
		}
	}

	return &VideoService{
		config:     cfg,
		httpClient: client,
	}
}

// ListCloudVideos 获取云端视频列表
func (s *VideoService) ListCloudVideos(filter *model.VideoFilter) (*model.VideoListResponse, error) {
	// 构建请求URL
	apiURL := fmt.Sprintf("%s/api/backup/videos", s.config.CloudAPI.BaseURL)
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 添加查询参数
	q := req.URL.Query()
	if filter.StartTime != nil {
		q.Add("start_time", strconv.FormatInt(*filter.StartTime, 10))
	}
	if filter.EndTime != nil {
		q.Add("end_time", strconv.FormatInt(*filter.EndTime, 10))
	}
	if filter.DeviceID != nil {
		q.Add("device_id", *filter.DeviceID)
	}
	if filter.CatID != nil {
		q.Add("cat_id", *filter.CatID)
	}
	if filter.Page > 0 {
		q.Add("page", strconv.Itoa(filter.Page))
	}
	if filter.Limit > 0 {
		q.Add("limit", strconv.Itoa(filter.Limit))
	}
	req.URL.RawQuery = q.Encode()

	// 添加认证头
	req.Header.Set("Authorization", "Bearer "+s.config.CloudAPI.AuthToken)

	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API请求失败: %d, %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response model.VideoListResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &response, nil
}

// GetCloudVideoInfo 获取云端视频详细信息
func (s *VideoService) GetCloudVideoInfo(videoID string) (*model.VideoInfo, error) {
	// 构建请求URL
	apiURL := fmt.Sprintf("%s/api/backup/videos/%s", s.config.CloudAPI.BaseURL, videoID)
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 添加认证头
	req.Header.Set("Authorization", "Bearer "+s.config.CloudAPI.AuthToken)

	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API请求失败: %d, %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var video model.VideoInfo
	if err := json.NewDecoder(resp.Body).Decode(&video); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &video, nil
}

// TestConnection 测试云端API连接
func (s *VideoService) TestConnection() error {
	// 尝试获取第一页视频列表来测试连接
	filter := &model.VideoFilter{
		Page:  1,
		Limit: 1,
	}

	_, err := s.ListCloudVideos(filter)
	return err
}
