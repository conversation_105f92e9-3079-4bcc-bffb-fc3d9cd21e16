package service

import (
	"backup-server/internal/config"
	"backup-server/internal/model"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// DownloadService 下载服务
type DownloadService struct {
	config         *config.Config
	videoService   *VideoService
	storageService *StorageService
	httpClient     *http.Client
	tasks          map[string]*model.DownloadTask
	tasksMutex     sync.RWMutex
	downloadQueue  chan *model.DownloadTask
	workers        int
	ctx            context.Context
	cancel         context.CancelFunc
}

// NewDownloadService 创建下载服务
func NewDownloadService(cfg *config.Config, videoService *VideoService, storageService *StorageService) *DownloadService {
	// 创建HTTP客户端，支持代理
	client := &http.Client{
		Timeout: cfg.Download.Timeout,
	}

	// 如果启用代理，设置代理
	if cfg.Proxy.Enabled {
		if proxyURL, err := url.Parse(cfg.Proxy.HTTPProxy); err == nil {
			client.Transport = &http.Transport{
				Proxy: http.ProxyURL(proxyURL),
			}
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	service := &DownloadService{
		config:         cfg,
		videoService:   videoService,
		storageService: storageService,
		httpClient:     client,
		tasks:          make(map[string]*model.DownloadTask),
		downloadQueue:  make(chan *model.DownloadTask, 100),
		workers:        cfg.Download.ConcurrentDownloads,
		ctx:            ctx,
		cancel:         cancel,
	}

	// 启动下载工作者
	service.startWorkers()

	return service
}

// startWorkers 启动下载工作者
func (s *DownloadService) startWorkers() {
	for i := 0; i < s.workers; i++ {
		go s.downloadWorker()
	}
}

// downloadWorker 下载工作者
func (s *DownloadService) downloadWorker() {
	for {
		select {
		case task := <-s.downloadQueue:
			s.processDownloadTask(task)
		case <-s.ctx.Done():
			return
		}
	}
}

// CreateDownloadTask 创建下载任务
func (s *DownloadService) CreateDownloadTask(videoID string) (*model.DownloadTask, error) {
	// 检查是否已经下载
	if s.storageService.IsVideoDownloaded(videoID) {
		return nil, fmt.Errorf("视频已存在: %s", videoID)
	}

	// 获取视频信息
	videoInfo, err := s.videoService.GetCloudVideoInfo(videoID)
	if err != nil {
		return nil, fmt.Errorf("获取视频信息失败: %v", err)
	}

	// 创建任务
	task := &model.DownloadTask{
		ID:             generateTaskID(),
		VideoID:        videoInfo.VideoID,
		DeviceID:       videoInfo.DeviceID,
		CatID:          videoInfo.CatID,
		Status:         "pending",
		Progress:       0,
		TotalFiles:     len(videoInfo.FileList),
		CompletedFiles: 0,
		TotalSize:      calculateTotalSize(videoInfo.FileList),
		DownloadedSize: 0,
		StartTime:      time.Now(),
	}

	// 保存任务
	s.tasksMutex.Lock()
	s.tasks[task.ID] = task
	s.tasksMutex.Unlock()

	// 添加到下载队列
	select {
	case s.downloadQueue <- task:
		return task, nil
	default:
		return nil, fmt.Errorf("下载队列已满")
	}
}

// processDownloadTask 处理下载任务
func (s *DownloadService) processDownloadTask(task *model.DownloadTask) {
	// 更新任务状态
	s.updateTaskStatus(task.ID, "downloading", "")

	// 获取视频详细信息
	videoInfo, err := s.videoService.GetCloudVideoInfo(task.VideoID)
	if err != nil {
		s.updateTaskStatus(task.ID, "failed", fmt.Sprintf("获取视频信息失败: %v", err))
		return
	}

	// 创建本地存储目录
	localPath := s.storageService.GetVideoPath(model.ViewModeByCat, task.DeviceID, task.CatID, videoInfo.StartTime)
	if err := os.MkdirAll(localPath, 0755); err != nil {
		s.updateTaskStatus(task.ID, "failed", fmt.Sprintf("创建目录失败: %v", err))
		return
	}

	// 下载文件
	var downloadedSize int64
	var completedFiles int
	var failedFiles []string

	for _, file := range videoInfo.FileList {
		// 检查任务是否被取消
		if s.isTaskCancelled(task.ID) {
			s.updateTaskStatus(task.ID, "cancelled", "任务被取消")
			return
		}

		// 下载文件
		filePath := filepath.Join(localPath, file.FileName)
		fileSize, err := s.downloadFile(file.DownloadURL, filePath)
		if err != nil {
			failedFiles = append(failedFiles, file.FileName)
			continue
		}

		downloadedSize += fileSize
		completedFiles++

		// 更新进度
		progress := float64(completedFiles) / float64(len(videoInfo.FileList)) * 100
		s.updateTaskProgress(task.ID, progress, completedFiles, downloadedSize)
	}

	// 保存元数据
	metadataPath := filepath.Join(localPath, "metadata.json")
	if err := s.saveVideoMetadata(videoInfo, metadataPath); err != nil {
		s.updateTaskStatus(task.ID, "failed", fmt.Sprintf("保存元数据失败: %v", err))
		return
	}

	// 计算校验和
	checksum, err := s.calculateDirectoryChecksum(localPath)
	if err != nil {
		checksum = ""
	}

	// 更新同步状态
	localVideo := model.LocalVideoInfo{
		VideoID:        videoInfo.VideoID,
		DeviceID:       videoInfo.DeviceID,
		CatID:          videoInfo.CatID,
		StartTime:      videoInfo.StartTime,
		EndTime:        videoInfo.EndTime,
		WeightLitter:   videoInfo.WeightLitter,
		WeightCat:      videoInfo.WeightCat,
		WeightWaste:    videoInfo.WeightWaste,
		BehaviorType:   videoInfo.BehaviorType,
		LocalPath:      localPath,
		DownloadedAt:   time.Now(),
		FileCount:      completedFiles,
		TotalSize:      downloadedSize,
		Checksum:       checksum,
		WeightData:     videoInfo.WeightData,
		Status:         "complete",
		LastChecked:    time.Now(),
	}

	if len(failedFiles) > 0 {
		localVideo.Status = "partial"
	}

	// 保存到同步状态
	if err := s.addToSyncStatus(&localVideo); err != nil {
		s.updateTaskStatus(task.ID, "failed", fmt.Sprintf("保存同步状态失败: %v", err))
		return
	}

	// 任务完成
	endTime := time.Now()
	s.tasksMutex.Lock()
	if task, exists := s.tasks[task.ID]; exists {
		task.Status = "completed"
		task.Progress = 100
		task.EndTime = &endTime
		if len(failedFiles) > 0 {
			task.Error = fmt.Sprintf("部分文件下载失败: %v", failedFiles)
		}
	}
	s.tasksMutex.Unlock()
}

// downloadFile 下载单个文件
func (s *DownloadService) downloadFile(url, filePath string) (int64, error) {
	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return 0, err
	}

	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("HTTP错误: %d", resp.StatusCode)
	}

	// 创建文件
	file, err := os.Create(filePath)
	if err != nil {
		return 0, err
	}
	defer file.Close()

	// 复制数据
	size, err := io.Copy(file, resp.Body)
	if err != nil {
		os.Remove(filePath) // 删除不完整的文件
		return 0, err
	}

	return size, nil
}

// saveVideoMetadata 保存视频元数据
func (s *DownloadService) saveVideoMetadata(video *model.VideoInfo, filePath string) error {
	data, err := json.MarshalIndent(video, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(filePath, data, 0644)
}

// calculateDirectoryChecksum 计算目录校验和
func (s *DownloadService) calculateDirectoryChecksum(dirPath string) (string, error) {
	hash := md5.New()

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			file, err := os.Open(path)
			if err != nil {
				return err
			}
			defer file.Close()

			if _, err := io.Copy(hash, file); err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// addToSyncStatus 添加到同步状态
func (s *DownloadService) addToSyncStatus(video *model.LocalVideoInfo) error {
	status, err := s.storageService.LoadSyncStatus()
	if err != nil {
		return err
	}

	status.DownloadedVideos[video.VideoID] = *video
	status.LastSync = time.Now()

	return s.storageService.SaveSyncStatus(status)
}

// 辅助函数
func generateTaskID() string {
	return fmt.Sprintf("task_%d", time.Now().UnixNano())
}

func calculateTotalSize(files []model.VideoFile) int64 {
	var total int64
	for _, file := range files {
		total += file.FileSize
	}
	return total
}

func (s *DownloadService) updateTaskStatus(taskID, status, errorMsg string) {
	s.tasksMutex.Lock()
	defer s.tasksMutex.Unlock()

	if task, exists := s.tasks[taskID]; exists {
		task.Status = status
		if errorMsg != "" {
			task.Error = errorMsg
		}
		if status == "failed" || status == "completed" || status == "cancelled" {
			endTime := time.Now()
			task.EndTime = &endTime
		}
	}
}

func (s *DownloadService) updateTaskProgress(taskID string, progress float64, completedFiles int, downloadedSize int64) {
	s.tasksMutex.Lock()
	defer s.tasksMutex.Unlock()

	if task, exists := s.tasks[taskID]; exists {
		task.Progress = progress
		task.CompletedFiles = completedFiles
		task.DownloadedSize = downloadedSize
	}
}

func (s *DownloadService) isTaskCancelled(taskID string) bool {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()

	if task, exists := s.tasks[taskID]; exists {
		return task.Status == "cancelled"
	}
	return false
}

// GetTask 获取任务信息
func (s *DownloadService) GetTask(taskID string) (*model.DownloadTask, error) {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("任务不存在: %s", taskID)
	}

	// 返回任务副本
	taskCopy := *task
	return &taskCopy, nil
}

// ListTasks 获取任务列表
func (s *DownloadService) ListTasks() []*model.DownloadTask {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()

	var tasks []*model.DownloadTask
	for _, task := range s.tasks {
		taskCopy := *task
		tasks = append(tasks, &taskCopy)
	}

	return tasks
}

// CancelTask 取消任务
func (s *DownloadService) CancelTask(taskID string) error {
	s.tasksMutex.Lock()
	defer s.tasksMutex.Unlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	if task.Status == "completed" || task.Status == "failed" {
		return fmt.Errorf("任务已完成，无法取消")
	}

	task.Status = "cancelled"
	endTime := time.Now()
	task.EndTime = &endTime

	return nil
}

// RetryTask 重试任务
func (s *DownloadService) RetryTask(taskID string) error {
	s.tasksMutex.Lock()
	task, exists := s.tasks[taskID]
	if !exists {
		s.tasksMutex.Unlock()
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	if task.Status != "failed" && task.Status != "cancelled" {
		s.tasksMutex.Unlock()
		return fmt.Errorf("只能重试失败或取消的任务")
	}

	// 重置任务状态
	task.Status = "pending"
	task.Progress = 0
	task.CompletedFiles = 0
	task.DownloadedSize = 0
	task.Error = ""
	task.StartTime = time.Now()
	task.EndTime = nil
	s.tasksMutex.Unlock()

	// 重新添加到队列
	select {
	case s.downloadQueue <- task:
		return nil
	default:
		return fmt.Errorf("下载队列已满")
	}
}

// GetTaskStatus 获取任务状态统计
func (s *DownloadService) GetTaskStatus() *model.TaskStatus {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()

	status := &model.TaskStatus{}
	for _, task := range s.tasks {
		status.Total++
		switch task.Status {
		case "pending":
			status.Pending++
		case "downloading":
			status.Downloading++
		case "completed":
			status.Completed++
		case "failed", "cancelled":
			status.Failed++
		}
	}

	return status
}

// CreateBatchDownloadTasks 批量创建下载任务
func (s *DownloadService) CreateBatchDownloadTasks(videoIDs []string) ([]*model.DownloadTask, []error) {
	var tasks []*model.DownloadTask
	var errors []error

	for _, videoID := range videoIDs {
		task, err := s.CreateDownloadTask(videoID)
		if err != nil {
			errors = append(errors, fmt.Errorf("创建任务失败 %s: %v", videoID, err))
			continue
		}
		tasks = append(tasks, task)
	}

	return tasks, errors
}

// Stop 停止下载服务
func (s *DownloadService) Stop() {
	s.cancel()
}
