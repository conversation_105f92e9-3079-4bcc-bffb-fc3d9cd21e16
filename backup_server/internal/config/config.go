package config

import (
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// Config 应用配置
type Config struct {
	Server   ServerConfig   `yaml:"server"`
	CloudAPI CloudAPIConfig `yaml:"cloud_api"`
	Proxy    ProxyConfig    `yaml:"proxy"`
	Storage  StorageConfig  `yaml:"storage"`
	Download DownloadConfig `yaml:"download"`
	Sync     SyncConfig     `yaml:"sync"`
	Log      LogConfig      `yaml:"log"`
	Web      WebConfig      `yaml:"web"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port string `yaml:"port"`
	Host string `yaml:"host"`
}

// CloudAPIConfig 云端API配置
type CloudAPIConfig struct {
	BaseURL   string        `yaml:"base_url"`
	AuthToken string        `yaml:"auth_token"`
	Timeout   time.Duration `yaml:"timeout"`
}

// ProxyConfig 代理配置
type ProxyConfig struct {
	HTTPProxy  string `yaml:"http_proxy"`
	HTTPSProxy string `yaml:"https_proxy"`
	AllProxy   string `yaml:"all_proxy"`
	Enabled    bool   `yaml:"enabled"`
}

// StorageConfig 存储配置
type StorageConfig struct {
	DataDir     string `yaml:"data_dir"`
	VideosDir   string `yaml:"videos_dir"`
	MetadataDir string `yaml:"metadata_dir"`
	SyncFile    string `yaml:"sync_file"`
}

// DownloadConfig 下载配置
type DownloadConfig struct {
	ConcurrentDownloads int           `yaml:"concurrent_downloads"`
	RetryAttempts       int           `yaml:"retry_attempts"`
	RetryDelay          time.Duration `yaml:"retry_delay"`
	ChunkSize           int64         `yaml:"chunk_size"`
	Timeout             time.Duration `yaml:"timeout"`
}

// SyncConfig 同步配置
type SyncConfig struct {
	CheckInterval time.Duration `yaml:"check_interval"`
	BatchSize     int           `yaml:"batch_size"`
	AutoSync      bool          `yaml:"auto_sync"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `yaml:"level"`
	File       string `yaml:"file"`
	MaxSize    int    `yaml:"max_size"`
	MaxBackups int    `yaml:"max_backups"`
	MaxAge     int    `yaml:"max_age"`
}

// WebConfig Web界面配置
type WebConfig struct {
	StaticDir     string `yaml:"static_dir"`
	TemplateDir   string `yaml:"template_dir"`
	UploadMaxSize int64  `yaml:"upload_max_size"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, err
	}

	return &config, nil
}

// GetAddr 获取服务器监听地址
func (c *Config) GetAddr() string {
	return c.Server.Host + ":" + c.Server.Port
}
