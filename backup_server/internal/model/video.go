package model

import "time"

// VideoInfo 视频信息
type VideoInfo struct {
	VideoID      string                 `json:"video_id"`
	DeviceID     string                 `json:"device_id"`
	CatID        string                 `json:"cat_id"`
	StartTime    int64                  `json:"start_time"`
	EndTime      *int64                 `json:"end_time"`
	WeightLitter float64                `json:"weight_litter"`
	WeightCat    float64                `json:"weight_cat"`
	WeightWaste  float64                `json:"weight_waste"`
	BehaviorType string                 `json:"behavior_type"`
	MinioPath    string                 `json:"minio_path"`
	FileList     []VideoFile            `json:"file_list"`
	WeightData   map[string]interface{} `json:"weight_data"`
	CreatedAt    string                 `json:"created_at"`
}

// VideoFile 视频文件信息
type VideoFile struct {
	FileName    string `json:"file_name"`
	FileType    string `json:"file_type"`
	DownloadURL string `json:"download_url"`
	FileSize    int64  `json:"file_size"`
}

// VideoListResponse 视频列表响应
type VideoListResponse struct {
	Videos []VideoInfo `json:"videos"`
	Total  int64       `json:"total"`
	Page   int         `json:"page"`
	Limit  int         `json:"limit"`
}

// LocalVideoInfo 本地视频信息
type LocalVideoInfo struct {
	VideoID        string                 `json:"video_id"`
	DeviceID       string                 `json:"device_id"`
	CatID          string                 `json:"cat_id"`
	StartTime      int64                  `json:"start_time"`
	EndTime        *int64                 `json:"end_time"`
	WeightLitter   float64                `json:"weight_litter"`
	WeightCat      float64                `json:"weight_cat"`
	WeightWaste    float64                `json:"weight_waste"`
	BehaviorType   string                 `json:"behavior_type"`
	LocalPath      string                 `json:"local_path"`      // 本地存储路径
	DownloadedAt   time.Time              `json:"downloaded_at"`   // 下载时间
	FileCount      int                    `json:"file_count"`      // 文件数量
	TotalSize      int64                  `json:"total_size"`      // 总大小
	Checksum       string                 `json:"checksum"`        // 校验和
	WeightData     map[string]interface{} `json:"weight_data"`     // 重量数据
	Status         string                 `json:"status"`          // 状态：complete, partial, error
	LastChecked    time.Time              `json:"last_checked"`    // 最后检查时间
}

// SyncStatus 同步状态
type SyncStatus struct {
	LastSync         time.Time                    `json:"last_sync"`
	DownloadedVideos map[string]LocalVideoInfo    `json:"downloaded_videos"`
	FailedDownloads  []FailedDownload             `json:"failed_downloads"`
	PendingDownloads []string                     `json:"pending_downloads"`
}

// FailedDownload 失败的下载记录
type FailedDownload struct {
	VideoID     string    `json:"video_id"`
	Error       string    `json:"error"`
	Attempts    int       `json:"attempts"`
	LastAttempt time.Time `json:"last_attempt"`
}

// DownloadTask 下载任务
type DownloadTask struct {
	ID          string    `json:"id"`
	VideoID     string    `json:"video_id"`
	DeviceID    string    `json:"device_id"`
	CatID       string    `json:"cat_id"`
	Status      string    `json:"status"`      // pending, downloading, completed, failed
	Progress    float64   `json:"progress"`    // 0-100
	TotalFiles  int       `json:"total_files"`
	CompletedFiles int    `json:"completed_files"`
	TotalSize   int64     `json:"total_size"`
	DownloadedSize int64  `json:"downloaded_size"`
	StartTime   time.Time `json:"start_time"`
	EndTime     *time.Time `json:"end_time"`
	Error       string    `json:"error"`
}

// TaskStatus 任务状态统计
type TaskStatus struct {
	Total       int `json:"total"`
	Pending     int `json:"pending"`
	Downloading int `json:"downloading"`
	Completed   int `json:"completed"`
	Failed      int `json:"failed"`
}

// VideoFilter 视频筛选条件
type VideoFilter struct {
	StartTime *int64  `json:"start_time"`
	EndTime   *int64  `json:"end_time"`
	DeviceID  *string `json:"device_id"`
	CatID     *string `json:"cat_id"`
	Page      int     `json:"page"`
	Limit     int     `json:"limit"`
}

// ViewMode 查看模式
type ViewMode string

const (
	ViewModeByCat    ViewMode = "by_cat"
	ViewModeByDevice ViewMode = "by_device"
)

// LocalVideoListRequest 本地视频列表请求
type LocalVideoListRequest struct {
	ViewMode ViewMode `json:"view_mode"`
	CatID    *string  `json:"cat_id"`
	DeviceID *string  `json:"device_id"`
	Page     int      `json:"page"`
	Limit    int      `json:"limit"`
}

// LocalVideoListResponse 本地视频列表响应
type LocalVideoListResponse struct {
	Videos   []LocalVideoInfo `json:"videos"`
	Total    int64            `json:"total"`
	Page     int              `json:"page"`
	Limit    int              `json:"limit"`
	ViewMode ViewMode         `json:"view_mode"`
}
