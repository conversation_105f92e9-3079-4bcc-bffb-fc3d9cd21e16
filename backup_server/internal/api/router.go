package api

import (
	"backup-server/internal/config"
	"backup-server/internal/model"
	"backup-server/internal/service"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// Router API路由器
type Router struct {
	config          *config.Config
	videoService    *service.VideoService
	storageService  *service.StorageService
	downloadService *service.DownloadService
	syncService     *service.SyncService
}

// NewRouter 创建新的路由器
func NewRouter(cfg *config.Config, videoService *service.VideoService, storageService *service.StorageService, downloadService *service.DownloadService, syncService *service.SyncService) *Router {
	return &Router{
		config:          cfg,
		videoService:    videoService,
		storageService:  storageService,
		downloadService: downloadService,
		syncService:     syncService,
	}
}

// SetupRoutes 设置路由
func (r *Router) SetupRoutes() *gin.Engine {
	// 设置Gin模式
	if r.config.Log.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// 添加CORS中间件
	router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 静态文件服务
	router.Static("/static", r.config.Web.StaticDir)
	router.LoadHTMLGlob(r.config.Web.TemplateDir + "/*")

	// 首页
	router.GET("/", r.renderIndex)

	// API路由组
	api := router.Group("/api")
	{
		// 云端视频相关
		cloud := api.Group("/cloud")
		{
			cloud.GET("/videos", r.listCloudVideos)
			cloud.GET("/videos/:video_id", r.getCloudVideoInfo)
		}

		// 本地视频相关
		local := api.Group("/local")
		{
			local.GET("/videos", r.listLocalVideos)
			local.GET("/videos/:video_id", r.getLocalVideoInfo)
			local.DELETE("/videos/:video_id", r.deleteLocalVideo)
		}

		// 下载任务相关
		tasks := api.Group("/tasks")
		{
			tasks.GET("", r.listTasks)
			tasks.POST("", r.createDownloadTask)
			tasks.GET("/:task_id", r.getTask)
			tasks.DELETE("/:task_id", r.cancelTask)
			tasks.POST("/:task_id/retry", r.retryTask)
			tasks.GET("/status", r.getTaskStatus)
		}

		// 同步相关
		sync := api.Group("/sync")
		{
			sync.GET("/status", r.getSyncStatus)
			sync.POST("/trigger", r.triggerSync)
			sync.GET("/diff", r.getSyncDiff)
		}

		// 系统信息
		system := api.Group("/system")
		{
			system.GET("/info", r.getSystemInfo)
			system.GET("/health", r.healthCheck)
		}
	}

	// WebSocket路由
	router.GET("/ws", r.handleWebSocket)

	return router
}

// renderIndex 渲染首页
func (r *Router) renderIndex(c *gin.Context) {
	c.HTML(http.StatusOK, "index.html", gin.H{
		"title": "视频备份系统",
	})
}

// healthCheck 健康检查
func (r *Router) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
		"time":   time.Now(),
	})
}

// ==================== 云端视频API ====================

// listCloudVideos 获取云端视频列表
func (r *Router) listCloudVideos(c *gin.Context) {
	var filter model.VideoFilter

	// 解析查询参数
	if startTime := c.Query("start_time"); startTime != "" {
		if timestamp, err := parseTimestamp(startTime); err == nil {
			filter.StartTime = &timestamp
		}
	}
	if endTime := c.Query("end_time"); endTime != "" {
		if timestamp, err := parseTimestamp(endTime); err == nil {
			filter.EndTime = &timestamp
		}
	}
	if deviceID := c.Query("device_id"); deviceID != "" {
		filter.DeviceID = &deviceID
	}
	if catID := c.Query("cat_id"); catID != "" {
		filter.CatID = &catID
	}

	filter.Page = parseIntWithDefault(c.Query("page"), 1)
	filter.Limit = parseIntWithDefault(c.Query("limit"), 20)

	// 获取视频列表
	response, err := r.videoService.ListCloudVideos(&filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// getCloudVideoInfo 获取云端视频详细信息
func (r *Router) getCloudVideoInfo(c *gin.Context) {
	videoID := c.Param("video_id")
	if videoID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "video_id参数不能为空"})
		return
	}

	video, err := r.videoService.GetCloudVideoInfo(videoID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, video)
}

// ==================== 本地视频API ====================

// listLocalVideos 获取本地视频列表
func (r *Router) listLocalVideos(c *gin.Context) {
	var req model.LocalVideoListRequest

	// 解析查询参数
	viewMode := c.Query("view_mode")
	if viewMode == "" {
		viewMode = "by_cat"
	}
	req.ViewMode = model.ViewMode(viewMode)

	if catID := c.Query("cat_id"); catID != "" {
		req.CatID = &catID
	}
	if deviceID := c.Query("device_id"); deviceID != "" {
		req.DeviceID = &deviceID
	}

	req.Page = parseIntWithDefault(c.Query("page"), 1)
	req.Limit = parseIntWithDefault(c.Query("limit"), 20)

	// 获取本地视频列表
	response, err := r.storageService.ListLocalVideos(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// getLocalVideoInfo 获取本地视频详细信息
func (r *Router) getLocalVideoInfo(c *gin.Context) {
	videoID := c.Param("video_id")
	if videoID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "video_id参数不能为空"})
		return
	}

	video, err := r.storageService.GetLocalVideoInfo(videoID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, video)
}

// deleteLocalVideo 删除本地视频
func (r *Router) deleteLocalVideo(c *gin.Context) {
	videoID := c.Param("video_id")
	if videoID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "video_id参数不能为空"})
		return
	}

	if err := r.storageService.DeleteLocalVideo(videoID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "视频删除成功"})
}

// ==================== 下载任务API ====================

// listTasks 获取任务列表
func (r *Router) listTasks(c *gin.Context) {
	tasks := r.downloadService.ListTasks()
	c.JSON(http.StatusOK, gin.H{
		"tasks": tasks,
		"total": len(tasks),
	})
}

// createDownloadTask 创建下载任务
func (r *Router) createDownloadTask(c *gin.Context) {
	var req struct {
		VideoIDs []string `json:"video_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数错误: " + err.Error()})
		return
	}

	if len(req.VideoIDs) == 1 {
		// 单个任务
		task, err := r.downloadService.CreateDownloadTask(req.VideoIDs[0])
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, task)
	} else {
		// 批量任务
		tasks, errors := r.downloadService.CreateBatchDownloadTasks(req.VideoIDs)
		response := gin.H{
			"tasks":         tasks,
			"created_count": len(tasks),
			"total_count":   len(req.VideoIDs),
		}
		if len(errors) > 0 {
			var errorMessages []string
			for _, err := range errors {
				errorMessages = append(errorMessages, err.Error())
			}
			response["errors"] = errorMessages
		}
		c.JSON(http.StatusOK, response)
	}
}

// getTask 获取任务详情
func (r *Router) getTask(c *gin.Context) {
	taskID := c.Param("task_id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "task_id参数不能为空"})
		return
	}

	task, err := r.downloadService.GetTask(taskID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, task)
}

// cancelTask 取消任务
func (r *Router) cancelTask(c *gin.Context) {
	taskID := c.Param("task_id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "task_id参数不能为空"})
		return
	}

	if err := r.downloadService.CancelTask(taskID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务取消成功"})
}

// retryTask 重试任务
func (r *Router) retryTask(c *gin.Context) {
	taskID := c.Param("task_id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "task_id参数不能为空"})
		return
	}

	if err := r.downloadService.RetryTask(taskID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务重试成功"})
}

// getTaskStatus 获取任务状态统计
func (r *Router) getTaskStatus(c *gin.Context) {
	status := r.downloadService.GetTaskStatus()
	c.JSON(http.StatusOK, status)
}

// ==================== 同步API ====================

// getSyncStatus 获取同步状态
func (r *Router) getSyncStatus(c *gin.Context) {
	status, err := r.syncService.GetSyncStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, status)
}

// triggerSync 触发同步
func (r *Router) triggerSync(c *gin.Context) {
	if err := r.syncService.TriggerSync(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "同步已触发"})
}

// getSyncDiff 获取同步差异
func (r *Router) getSyncDiff(c *gin.Context) {
	diff, err := r.syncService.GetSyncDiff()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, diff)
}

// ==================== 系统信息API ====================

// getSystemInfo 获取系统信息
func (r *Router) getSystemInfo(c *gin.Context) {
	// 获取存储统计
	storageStats, err := r.storageService.GetStorageStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 获取猫咪列表
	cats, err := r.storageService.GetCatList()
	if err != nil {
		cats = []string{}
	}

	// 获取设备列表
	devices, err := r.storageService.GetDeviceList()
	if err != nil {
		devices = []string{}
	}

	// 获取任务状态
	taskStatus := r.downloadService.GetTaskStatus()

	c.JSON(http.StatusOK, gin.H{
		"storage_stats": storageStats,
		"cats":          cats,
		"devices":       devices,
		"task_status":   taskStatus,
		"config": gin.H{
			"concurrent_downloads": r.config.Download.ConcurrentDownloads,
			"retry_attempts":       r.config.Download.RetryAttempts,
			"auto_sync":            r.config.Sync.AutoSync,
			"check_interval":       r.config.Sync.CheckInterval.String(),
		},
	})
}

// ==================== WebSocket ====================

// handleWebSocket 处理WebSocket连接
func (r *Router) handleWebSocket(c *gin.Context) {
	// TODO: 实现WebSocket连接处理
	// 用于实时推送下载进度和同步状态
	c.JSON(http.StatusNotImplemented, gin.H{"error": "WebSocket功能待实现"})
}

// ==================== 辅助函数 ====================

// parseTimestamp 解析时间戳
func parseTimestamp(s string) (int64, error) {
	// 尝试解析Unix时间戳
	if timestamp, err := strconv.ParseInt(s, 10, 64); err == nil {
		return timestamp, nil
	}

	// 尝试解析RFC3339格式
	if t, err := time.Parse(time.RFC3339, s); err == nil {
		return t.Unix(), nil
	}

	return 0, fmt.Errorf("无效的时间格式")
}

// parseIntWithDefault 解析整数，失败时返回默认值
func parseIntWithDefault(s string, defaultValue int) int {
	if s == "" {
		return defaultValue
	}

	if value, err := strconv.Atoi(s); err == nil {
		return value
	}

	return defaultValue
}
