// 主应用程序

class BackupApp {
    constructor() {
        this.currentTab = 'cloud';
        this.selectedVideos = new Set();
        this.refreshInterval = null;
        this.systemInfo = null;
        
        this.init();
    }

    async init() {
        this.bindEvents();
        this.startPeriodicRefresh();
        
        // 加载系统信息
        await this.loadSystemInfo();
        
        // 加载默认标签页
        this.switchTab('cloud');
        
        // 检查连接状态
        this.checkConnectionStatus();
    }

    bindEvents() {
        // 标签页切换
        document.querySelectorAll('.nav-item[data-tab]').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = e.target.closest('.nav-item').dataset.tab;
                this.switchTab(tab);
            });
        });

        // 同步按钮
        document.getElementById('sync-btn').addEventListener('click', () => {
            this.triggerSync();
        });

        // 云端视频相关事件
        this.bindCloudVideoEvents();
        
        // 本地视频相关事件
        this.bindLocalVideoEvents();
        
        // 任务管理相关事件
        this.bindTaskEvents();

        // 全局事件监听
        eventBus.on('switchTab', (tab) => {
            this.switchTab(tab);
        });

        eventBus.on('refreshData', () => {
            this.refreshCurrentTab();
        });
    }

    bindCloudVideoEvents() {
        // 全选复选框
        document.getElementById('select-all-cloud').addEventListener('change', (e) => {
            const checkboxes = document.querySelectorAll('#cloud-video-grid .video-checkbox input');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
                const videoId = checkbox.dataset.videoId;
                if (e.target.checked) {
                    this.selectedVideos.add(videoId);
                } else {
                    this.selectedVideos.delete(videoId);
                }
            });
            this.updateBatchDownloadButton();
        });

        // 批量下载按钮
        document.getElementById('batch-download-btn').addEventListener('click', () => {
            this.batchDownload();
        });

        // 筛选按钮
        document.getElementById('cloud-filter-btn').addEventListener('click', () => {
            this.loadCloudVideos(1);
        });
    }

    bindLocalVideoEvents() {
        // 查看模式切换
        document.querySelectorAll('.view-mode-toggle .btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const mode = e.target.dataset.mode;
                this.switchViewMode(mode);
            });
        });

        // 本地筛选按钮
        document.getElementById('local-filter-btn').addEventListener('click', () => {
            this.loadLocalVideos(1);
        });
    }

    bindTaskEvents() {
        // 清理已完成任务
        document.getElementById('clear-completed-btn').addEventListener('click', () => {
            this.clearCompletedTasks();
        });

        // 重试失败任务
        document.getElementById('retry-failed-btn').addEventListener('click', () => {
            this.retryFailedTasks();
        });
    }

    switchTab(tab) {
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`.nav-item[data-tab="${tab}"]`).classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tab}-tab`).classList.add('active');

        this.currentTab = tab;
        this.refreshCurrentTab();
    }

    async refreshCurrentTab() {
        switch (this.currentTab) {
            case 'cloud':
                await this.loadCloudVideos();
                break;
            case 'local':
                await this.loadLocalVideos();
                break;
            case 'tasks':
                await this.loadTasks();
                break;
        }
    }

    async loadSystemInfo() {
        try {
            this.systemInfo = await api.getSystemInfo();
            this.updateSystemInfo();
        } catch (error) {
            console.error('Failed to load system info:', error);
        }
    }

    updateSystemInfo() {
        if (!this.systemInfo) return;

        // 更新设备和猫咪筛选选项
        this.updateFilterOptions();
    }

    updateFilterOptions() {
        const cloudDeviceFilter = document.getElementById('cloud-device-filter');
        const cloudCatFilter = document.getElementById('cloud-cat-filter');
        const localFilter = document.getElementById('local-filter');

        // 清空现有选项
        cloudDeviceFilter.innerHTML = '<option value="">所有设备</option>';
        cloudCatFilter.innerHTML = '<option value="">所有猫咪</option>';

        // 添加设备选项
        if (this.systemInfo.devices) {
            this.systemInfo.devices.forEach(device => {
                cloudDeviceFilter.innerHTML += `<option value="${device}">${device}</option>`;
            });
        }

        // 添加猫咪选项
        if (this.systemInfo.cats) {
            this.systemInfo.cats.forEach(cat => {
                cloudCatFilter.innerHTML += `<option value="${cat}">${cat}</option>`;
            });
        }

        // 更新本地筛选选项
        this.updateLocalFilterOptions();
    }

    updateLocalFilterOptions() {
        const localFilter = document.getElementById('local-filter');
        const viewMode = document.querySelector('.view-mode-toggle .btn.active').dataset.mode;
        
        localFilter.innerHTML = '<option value="">全部</option>';
        
        if (viewMode === 'by_cat' && this.systemInfo.cats) {
            this.systemInfo.cats.forEach(cat => {
                localFilter.innerHTML += `<option value="${cat}">${cat}</option>`;
            });
        } else if (viewMode === 'by_device' && this.systemInfo.devices) {
            this.systemInfo.devices.forEach(device => {
                localFilter.innerHTML += `<option value="${device}">${device}</option>`;
            });
        }
    }

    async loadCloudVideos(page = 1) {
        try {
            const params = this.getCloudFilterParams();
            params.page = page;
            params.limit = 20;

            const response = await api.getCloudVideos(params);
            this.renderCloudVideos(response);
            this.renderCloudPagination(response);
        } catch (error) {
            notificationManager.error(`加载云端视频失败: ${error.message}`);
        }
    }

    getCloudFilterParams() {
        const params = {};
        
        const deviceId = document.getElementById('cloud-device-filter').value;
        if (deviceId) params.device_id = deviceId;
        
        const catId = document.getElementById('cloud-cat-filter').value;
        if (catId) params.cat_id = catId;
        
        const startDate = document.getElementById('cloud-start-date').value;
        if (startDate) {
            params.start_time = Math.floor(new Date(startDate).getTime() / 1000);
        }
        
        const endDate = document.getElementById('cloud-end-date').value;
        if (endDate) {
            params.end_time = Math.floor(new Date(endDate + ' 23:59:59').getTime() / 1000);
        }
        
        return params;
    }

    renderCloudVideos(response) {
        const grid = document.getElementById('cloud-video-grid');
        const countElement = document.getElementById('cloud-video-count');
        
        countElement.textContent = response.total;
        
        if (response.videos.length === 0) {
            grid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-cloud"></i>
                    <h3>暂无视频</h3>
                    <p>没有找到符合条件的云端视频</p>
                </div>
            `;
            return;
        }

        grid.innerHTML = response.videos.map(video => this.createVideoCard(video, false)).join('');
        
        // 绑定事件
        this.bindVideoCardEvents(grid, false);
    }

    createVideoCard(video, isLocal) {
        const startTime = formatTimestamp(video.start_time);
        const duration = video.end_time ? formatDuration(video.end_time - video.start_time) : '未知';
        
        return `
            <div class="video-card" data-video-id="${video.video_id}">
                ${!isLocal ? `
                    <div class="video-card-checkbox">
                        <label class="checkbox-container">
                            <input type="checkbox" class="video-checkbox" data-video-id="${video.video_id}">
                            <span class="checkmark"></span>
                        </label>
                    </div>
                ` : ''}
                
                <div class="video-card-header">
                    <i class="${getVideoTypeIcon(video.behavior_type)}"></i>
                    ${isLocal ? `<div class="video-card-status downloaded">已下载</div>` : ''}
                </div>
                
                <div class="video-card-body">
                    <div class="video-card-title">${video.video_id}</div>
                    
                    <div class="video-card-meta">
                        <div class="video-card-meta-item">
                            <i class="${getDeviceIcon(video.device_id)}"></i>
                            <span>${video.device_id}</span>
                        </div>
                        <div class="video-card-meta-item">
                            <i class="${getCatIcon(video.cat_id)}"></i>
                            <span>${video.cat_id || '未知'}</span>
                        </div>
                        <div class="video-card-meta-item">
                            <i class="fas fa-clock"></i>
                            <span>${startTime}</span>
                        </div>
                        <div class="video-card-meta-item">
                            <i class="fas fa-hourglass-half"></i>
                            <span>${duration}</span>
                        </div>
                        <div class="video-card-meta-item">
                            <i class="fas fa-weight"></i>
                            <span>${video.weight_cat} kg</span>
                        </div>
                    </div>
                    
                    <div class="video-card-actions">
                        <button class="btn btn-outline btn-details" data-video-id="${video.video_id}">
                            <i class="fas fa-info-circle"></i>
                            详情
                        </button>
                        ${!isLocal ? `
                            <button class="btn btn-primary btn-download" data-video-id="${video.video_id}">
                                <i class="fas fa-download"></i>
                                下载
                            </button>
                        ` : `
                            <button class="btn btn-danger btn-delete" data-video-id="${video.video_id}">
                                <i class="fas fa-trash"></i>
                                删除
                            </button>
                        `}
                    </div>
                </div>
            </div>
        `;
    }

    bindVideoCardEvents(container, isLocal) {
        // 复选框事件（仅云端视频）
        if (!isLocal) {
            container.querySelectorAll('.video-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', (e) => {
                    const videoId = e.target.dataset.videoId;
                    if (e.target.checked) {
                        this.selectedVideos.add(videoId);
                    } else {
                        this.selectedVideos.delete(videoId);
                    }
                    this.updateBatchDownloadButton();
                });
            });
        }

        // 详情按钮事件
        container.querySelectorAll('.btn-details').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const videoId = e.target.closest('.btn-details').dataset.videoId;
                modalManager.showVideoDetails(videoId, isLocal);
            });
        });

        // 下载按钮事件（仅云端视频）
        if (!isLocal) {
            container.querySelectorAll('.btn-download').forEach(btn => {
                btn.addEventListener('click', async (e) => {
                    const videoId = e.target.closest('.btn-download').dataset.videoId;
                    try {
                        await api.createDownloadTask([videoId]);
                        notificationManager.success('下载任务已创建');
                        this.switchTab('tasks');
                    } catch (error) {
                        notificationManager.error(`创建下载任务失败: ${error.message}`);
                    }
                });
            });
        } else {
            // 删除按钮事件（仅本地视频）
            container.querySelectorAll('.btn-delete').forEach(btn => {
                btn.addEventListener('click', async (e) => {
                    const videoId = e.target.closest('.btn-delete').dataset.videoId;
                    if (confirm('确定要删除这个本地视频吗？此操作不可恢复。')) {
                        try {
                            await api.deleteLocalVideo(videoId);
                            notificationManager.success('视频删除成功');
                            this.loadLocalVideos();
                        } catch (error) {
                            notificationManager.error(`删除视频失败: ${error.message}`);
                        }
                    }
                });
            });
        }
    }

    updateBatchDownloadButton() {
        const btn = document.getElementById('batch-download-btn');
        const count = this.selectedVideos.size;
        
        btn.disabled = count === 0;
        btn.innerHTML = `
            <i class="fas fa-download"></i>
            批量下载 ${count > 0 ? `(${count})` : ''}
        `;
    }

    async batchDownload() {
        if (this.selectedVideos.size === 0) return;
        
        try {
            const videoIds = Array.from(this.selectedVideos);
            await api.createDownloadTask(videoIds);
            
            notificationManager.success(`已创建 ${videoIds.length} 个下载任务`);
            this.selectedVideos.clear();
            this.updateBatchDownloadButton();
            
            // 取消全选
            document.getElementById('select-all-cloud').checked = false;
            document.querySelectorAll('#cloud-video-grid .video-checkbox input').forEach(checkbox => {
                checkbox.checked = false;
            });
            
            this.switchTab('tasks');
        } catch (error) {
            notificationManager.error(`批量下载失败: ${error.message}`);
        }
    }

    renderCloudPagination(response) {
        const container = document.getElementById('cloud-pagination');
        const totalPages = Math.ceil(response.total / response.limit);
        
        if (!this.cloudPagination) {
            this.cloudPagination = new PaginationComponent(container, {
                onPageChange: (page) => this.loadCloudVideos(page)
            });
        }
        
        this.cloudPagination.render(response.page, totalPages);
    }

    startPeriodicRefresh() {
        // 每30秒刷新一次任务状态和系统状态
        this.refreshInterval = setInterval(() => {
            if (this.currentTab === 'tasks') {
                this.loadTasks();
            }
            this.updateConnectionStatus();
        }, 30000);
    }

    async checkConnectionStatus() {
        try {
            await api.healthCheck();
            this.updateConnectionStatus(true);
        } catch (error) {
            this.updateConnectionStatus(false);
        }
    }

    updateConnectionStatus(isConnected = null) {
        const indicator = document.getElementById('status-indicator');
        const dot = indicator.querySelector('.status-dot');
        const text = indicator.querySelector('.status-text');
        
        if (isConnected === null) {
            // 检查连接状态
            this.checkConnectionStatus();
            return;
        }
        
        if (isConnected) {
            dot.className = 'status-dot';
            text.textContent = '已连接';
        } else {
            dot.className = 'status-dot error';
            text.textContent = '连接失败';
        }
    }

    async triggerSync() {
        try {
            await api.triggerSync();
            notificationManager.success('同步已触发');
            
            // 刷新当前标签页
            setTimeout(() => {
                this.refreshCurrentTab();
            }, 2000);
        } catch (error) {
            notificationManager.error(`同步失败: ${error.message}`);
        }
    }

    // 本地视频相关方法
    switchViewMode(mode) {
        // 更新按钮状态
        document.querySelectorAll('.view-mode-toggle .btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`.view-mode-toggle .btn[data-mode="${mode}"]`).classList.add('active');

        // 更新筛选选项
        this.updateLocalFilterOptions();

        // 重新加载数据
        this.loadLocalVideos(1);
    }

    async loadLocalVideos(page = 1) {
        try {
            const params = this.getLocalFilterParams();
            params.page = page;
            params.limit = 20;

            const response = await api.getLocalVideos(params);
            this.renderLocalVideos(response);
            this.renderLocalPagination(response);
        } catch (error) {
            notificationManager.error(`加载本地视频失败: ${error.message}`);
        }
    }

    getLocalFilterParams() {
        const params = {};
        const viewMode = document.querySelector('.view-mode-toggle .btn.active').dataset.mode;
        params.view_mode = viewMode;

        const filterValue = document.getElementById('local-filter').value;
        if (filterValue) {
            if (viewMode === 'by_cat') {
                params.cat_id = filterValue;
            } else {
                params.device_id = filterValue;
            }
        }

        return params;
    }

    renderLocalVideos(response) {
        const grid = document.getElementById('local-video-grid');
        const countElement = document.getElementById('local-video-count');
        const sizeElement = document.getElementById('storage-size');

        countElement.textContent = response.total;

        // 计算总存储大小
        const totalSize = response.videos.reduce((sum, video) => sum + (video.total_size || 0), 0);
        sizeElement.textContent = formatFileSize(totalSize);

        if (response.videos.length === 0) {
            grid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-hdd"></i>
                    <h3>暂无本地视频</h3>
                    <p>还没有下载任何视频到本地</p>
                </div>
            `;
            return;
        }

        grid.innerHTML = response.videos.map(video => this.createVideoCard(video, true)).join('');

        // 绑定事件
        this.bindVideoCardEvents(grid, true);
    }

    renderLocalPagination(response) {
        const container = document.getElementById('local-pagination');
        const totalPages = Math.ceil(response.total / response.limit);

        if (!this.localPagination) {
            this.localPagination = new PaginationComponent(container, {
                onPageChange: (page) => this.loadLocalVideos(page)
            });
        }

        this.localPagination.render(response.page, totalPages);
    }

    // 任务管理相关方法
    async loadTasks() {
        try {
            const [tasksResponse, statusResponse] = await Promise.all([
                api.getTasks(),
                api.getTaskStatus()
            ]);

            this.renderTaskStats(statusResponse);
            this.renderTasks(tasksResponse.tasks);
        } catch (error) {
            notificationManager.error(`加载任务失败: ${error.message}`);
        }
    }

    renderTaskStats(stats) {
        document.getElementById('total-tasks').textContent = stats.total;
        document.getElementById('pending-tasks').textContent = stats.pending;
        document.getElementById('downloading-tasks').textContent = stats.downloading;
        document.getElementById('completed-tasks').textContent = stats.completed;
        document.getElementById('failed-tasks').textContent = stats.failed;
    }

    renderTasks(tasks) {
        const container = document.getElementById('task-list');

        if (tasks.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-tasks"></i>
                    <h3>暂无任务</h3>
                    <p>还没有创建任何下载任务</p>
                </div>
            `;
            return;
        }

        // 按状态和时间排序
        tasks.sort((a, b) => {
            const statusOrder = { downloading: 0, pending: 1, failed: 2, completed: 3, cancelled: 4 };
            const statusDiff = statusOrder[a.status] - statusOrder[b.status];
            if (statusDiff !== 0) return statusDiff;

            return new Date(b.start_time) - new Date(a.start_time);
        });

        container.innerHTML = tasks.map(task => this.createTaskCard(task)).join('');

        // 绑定任务事件
        this.bindTaskCardEvents(container);
    }

    createTaskCard(task) {
        const progress = Math.round(task.progress || 0);
        const progressText = formatProgressText(
            task.completed_files,
            task.total_files,
            task.downloaded_size,
            task.total_size
        );

        const startTime = formatTimestamp(new Date(task.start_time).getTime() / 1000);
        const endTime = task.end_time ? formatTimestamp(new Date(task.end_time).getTime() / 1000) : '';

        return `
            <div class="task-card ${task.status}" data-task-id="${task.id}">
                <div class="task-card-header">
                    <div>
                        <div class="task-card-title">${task.video_id}</div>
                        <div class="task-card-subtitle">${task.device_id} • ${task.cat_id}</div>
                    </div>
                    <div class="task-card-status ${task.status}">${getTaskStatusText(task.status)}</div>
                </div>

                ${task.status === 'downloading' || task.status === 'completed' ? `
                    <div class="task-card-progress">
                        <div class="progress-bar">
                            <div class="progress-bar-fill ${task.status}" style="width: ${progress}%"></div>
                        </div>
                        <div class="progress-text">
                            <span>${progress}%</span>
                            <span>${progressText}</span>
                        </div>
                    </div>
                ` : ''}

                <div class="task-card-meta">
                    <div>开始时间: ${startTime}</div>
                    ${endTime ? `<div>结束时间: ${endTime}</div>` : ''}
                    <div>文件数: ${task.total_files}</div>
                    <div>总大小: ${formatFileSize(task.total_size)}</div>
                </div>

                ${task.error ? `
                    <div class="task-card-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${task.error}
                    </div>
                ` : ''}

                <div class="task-card-actions">
                    ${task.status === 'failed' || task.status === 'cancelled' ? `
                        <button class="btn btn-warning btn-retry" data-task-id="${task.id}">
                            <i class="fas fa-redo"></i>
                            重试
                        </button>
                    ` : ''}
                    ${task.status === 'pending' || task.status === 'downloading' ? `
                        <button class="btn btn-secondary btn-cancel" data-task-id="${task.id}">
                            <i class="fas fa-times"></i>
                            取消
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    bindTaskCardEvents(container) {
        // 重试按钮
        container.querySelectorAll('.btn-retry').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const taskId = e.target.closest('.btn-retry').dataset.taskId;
                try {
                    await api.retryTask(taskId);
                    notificationManager.success('任务重试成功');
                    this.loadTasks();
                } catch (error) {
                    notificationManager.error(`任务重试失败: ${error.message}`);
                }
            });
        });

        // 取消按钮
        container.querySelectorAll('.btn-cancel').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const taskId = e.target.closest('.btn-cancel').dataset.taskId;
                if (confirm('确定要取消这个任务吗？')) {
                    try {
                        await api.cancelTask(taskId);
                        notificationManager.success('任务取消成功');
                        this.loadTasks();
                    } catch (error) {
                        notificationManager.error(`任务取消失败: ${error.message}`);
                    }
                }
            });
        });
    }

    async clearCompletedTasks() {
        // TODO: 实现清理已完成任务的功能
        notificationManager.info('清理已完成任务功能待实现');
    }

    async retryFailedTasks() {
        // TODO: 实现重试所有失败任务的功能
        notificationManager.info('重试失败任务功能待实现');
    }

    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }
}

// 应用程序启动
document.addEventListener('DOMContentLoaded', () => {
    window.app = new BackupApp();
});
