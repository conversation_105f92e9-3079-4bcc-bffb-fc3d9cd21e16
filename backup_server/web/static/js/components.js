// 组件管理

// 通知管理器
class NotificationManager {
    constructor() {
        this.container = document.getElementById('notifications');
        this.notifications = new Map();
    }

    show(message, type = 'info', duration = 5000) {
        const id = generateId();
        const notification = this.createNotification(id, message, type);
        
        this.container.appendChild(notification);
        this.notifications.set(id, notification);

        // 显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                this.hide(id);
            }, duration);
        }

        return id;
    }

    hide(id) {
        const notification = this.notifications.get(id);
        if (notification) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                this.notifications.delete(id);
            }, 300);
        }
    }

    createNotification(id, message, type) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-header">
                <div class="notification-title">${this.getTypeTitle(type)}</div>
                <button class="notification-close" onclick="notificationManager.hide('${id}')">&times;</button>
            </div>
            <div class="notification-message">${message}</div>
        `;
        return notification;
    }

    getTypeTitle(type) {
        switch (type) {
            case 'success': return '成功';
            case 'warning': return '警告';
            case 'error': return '错误';
            default: return '信息';
        }
    }

    success(message, duration = 3000) {
        return this.show(message, 'success', duration);
    }

    warning(message, duration = 5000) {
        return this.show(message, 'warning', duration);
    }

    error(message, duration = 7000) {
        return this.show(message, 'error', duration);
    }

    info(message, duration = 5000) {
        return this.show(message, 'info', duration);
    }

    clear() {
        this.notifications.forEach((notification, id) => {
            this.hide(id);
        });
    }
}

// 模态框管理器
class ModalManager {
    constructor() {
        this.modal = document.getElementById('video-modal');
        this.modalTitle = document.getElementById('modal-title');
        this.modalBody = document.getElementById('modal-body');
        this.modalDownloadBtn = document.getElementById('modal-download-btn');
        this.currentVideoId = null;

        this.bindEvents();
    }

    bindEvents() {
        // 关闭按钮事件
        this.modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => this.hide());
        });

        // 点击背景关闭
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hide();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.hide();
            }
        });

        // 下载按钮事件
        this.modalDownloadBtn.addEventListener('click', () => {
            if (this.currentVideoId) {
                this.downloadVideo(this.currentVideoId);
            }
        });
    }

    show() {
        this.modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    hide() {
        this.modal.classList.remove('show');
        document.body.style.overflow = '';
        this.currentVideoId = null;
    }

    async showVideoDetails(videoId, isLocal = false) {
        this.currentVideoId = videoId;
        this.modalTitle.textContent = '视频详情';
        this.modalBody.innerHTML = '<div class="loading-text">加载中...</div>';
        
        // 根据是否为本地视频决定是否显示下载按钮
        this.modalDownloadBtn.style.display = isLocal ? 'none' : 'inline-flex';
        
        this.show();

        try {
            const video = isLocal 
                ? await api.getLocalVideoInfo(videoId)
                : await api.getCloudVideoInfo(videoId);
            
            this.renderVideoDetails(video, isLocal);
        } catch (error) {
            this.modalBody.innerHTML = `<div class="error-message">加载失败: ${error.message}</div>`;
        }
    }

    renderVideoDetails(video, isLocal) {
        const startTime = formatTimestamp(video.start_time);
        const endTime = video.end_time ? formatTimestamp(video.end_time) : '未知';
        const duration = video.end_time ? formatDuration(video.end_time - video.start_time) : '未知';

        let filesInfo = '';
        if (video.file_list && video.file_list.length > 0) {
            const totalSize = video.file_list.reduce((sum, file) => sum + file.file_size, 0);
            filesInfo = `
                <div class="detail-section">
                    <h4>文件列表 (${video.file_list.length} 个文件, ${formatFileSize(totalSize)})</h4>
                    <div class="file-list">
                        ${video.file_list.map(file => `
                            <div class="file-item">
                                <div class="file-info">
                                    <span class="file-name">${file.file_name}</span>
                                    <span class="file-size">${formatFileSize(file.file_size)}</span>
                                </div>
                                <span class="file-type">${file.file_type}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        let weightInfo = '';
        if (video.weight_data && Object.keys(video.weight_data).length > 0) {
            weightInfo = `
                <div class="detail-section">
                    <h4>重量数据</h4>
                    <pre class="weight-data">${JSON.stringify(video.weight_data, null, 2)}</pre>
                </div>
            `;
        }

        let localInfo = '';
        if (isLocal) {
            localInfo = `
                <div class="detail-section">
                    <h4>本地信息</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <span class="detail-label">本地路径:</span>
                            <span class="detail-value">${video.local_path}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">下载时间:</span>
                            <span class="detail-value">${formatTimestamp(new Date(video.downloaded_at).getTime() / 1000)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">文件数量:</span>
                            <span class="detail-value">${video.file_count}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">总大小:</span>
                            <span class="detail-value">${formatFileSize(video.total_size)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">状态:</span>
                            <span class="detail-value status-${video.status}">${video.status}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        this.modalBody.innerHTML = `
            <div class="video-details">
                <div class="detail-section">
                    <h4>基本信息</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <span class="detail-label">视频ID:</span>
                            <span class="detail-value">${video.video_id}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">设备ID:</span>
                            <span class="detail-value">${video.device_id}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">猫咪ID:</span>
                            <span class="detail-value">${video.cat_id || '未知'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">行为类型:</span>
                            <span class="detail-value">${video.behavior_type}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">开始时间:</span>
                            <span class="detail-value">${startTime}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">结束时间:</span>
                            <span class="detail-value">${endTime}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">持续时间:</span>
                            <span class="detail-value">${duration}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>重量信息</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <span class="detail-label">猫砂重量:</span>
                            <span class="detail-value">${video.weight_litter} kg</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">猫咪重量:</span>
                            <span class="detail-value">${video.weight_cat} kg</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">排泄物重量:</span>
                            <span class="detail-value">${video.weight_waste} kg</span>
                        </div>
                    </div>
                </div>

                ${localInfo}
                ${filesInfo}
                ${weightInfo}
            </div>
        `;
    }

    async downloadVideo(videoId) {
        try {
            await api.createDownloadTask([videoId]);
            notificationManager.success('下载任务已创建');
            this.hide();
            
            // 切换到任务管理页面
            eventBus.emit('switchTab', 'tasks');
        } catch (error) {
            notificationManager.error(`创建下载任务失败: ${error.message}`);
        }
    }
}

// 分页组件
class PaginationComponent {
    constructor(container, options = {}) {
        this.container = container;
        this.currentPage = 1;
        this.totalPages = 1;
        this.onPageChange = options.onPageChange || (() => {});
        this.maxVisiblePages = options.maxVisiblePages || 7;
    }

    render(currentPage, totalPages) {
        this.currentPage = currentPage;
        this.totalPages = totalPages;

        if (totalPages <= 1) {
            this.container.innerHTML = '';
            return;
        }

        const pages = this.generatePageNumbers();
        
        this.container.innerHTML = `
            <button class="btn btn-outline" ${currentPage <= 1 ? 'disabled' : ''} data-page="${currentPage - 1}">
                <i class="fas fa-chevron-left"></i>
            </button>
            ${pages.map(page => {
                if (page === '...') {
                    return '<span class="pagination-ellipsis">...</span>';
                } else {
                    return `<button class="btn ${page === currentPage ? 'btn-primary' : 'btn-outline'}" data-page="${page}">${page}</button>`;
                }
            }).join('')}
            <button class="btn btn-outline" ${currentPage >= totalPages ? 'disabled' : ''} data-page="${currentPage + 1}">
                <i class="fas fa-chevron-right"></i>
            </button>
        `;

        // 绑定事件
        this.container.querySelectorAll('button[data-page]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const page = parseInt(e.target.closest('button').dataset.page);
                if (page !== this.currentPage && page >= 1 && page <= this.totalPages) {
                    this.onPageChange(page);
                }
            });
        });
    }

    generatePageNumbers() {
        const pages = [];
        const { currentPage, totalPages, maxVisiblePages } = this;

        if (totalPages <= maxVisiblePages) {
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            const halfVisible = Math.floor(maxVisiblePages / 2);
            let startPage = Math.max(1, currentPage - halfVisible);
            let endPage = Math.min(totalPages, currentPage + halfVisible);

            if (currentPage <= halfVisible) {
                endPage = maxVisiblePages;
            } else if (currentPage >= totalPages - halfVisible) {
                startPage = totalPages - maxVisiblePages + 1;
            }

            if (startPage > 1) {
                pages.push(1);
                if (startPage > 2) {
                    pages.push('...');
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                pages.push(i);
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    pages.push('...');
                }
                pages.push(totalPages);
            }
        }

        return pages;
    }
}

// 全局组件实例
window.notificationManager = new NotificationManager();
window.modalManager = new ModalManager();
