// API接口管理

class API {
    constructor() {
        this.baseURL = '';
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }

    // 通用请求方法
    async request(url, options = {}) {
        const config = {
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };

        try {
            const response = await fetch(this.baseURL + url, config);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
            }

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            console.error('API request error:', error);
            throw error;
        }
    }

    // GET请求
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request(fullUrl, { method: 'GET' });
    }

    // POST请求
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT请求
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE请求
    async delete(url) {
        return this.request(url, { method: 'DELETE' });
    }

    // ==================== 云端视频API ====================

    // 获取云端视频列表
    async getCloudVideos(params = {}) {
        return this.get('/api/cloud/videos', params);
    }

    // 获取云端视频详情
    async getCloudVideoInfo(videoId) {
        return this.get(`/api/cloud/videos/${videoId}`);
    }

    // ==================== 本地视频API ====================

    // 获取本地视频列表
    async getLocalVideos(params = {}) {
        return this.get('/api/local/videos', params);
    }

    // 获取本地视频详情
    async getLocalVideoInfo(videoId) {
        return this.get(`/api/local/videos/${videoId}`);
    }

    // 删除本地视频
    async deleteLocalVideo(videoId) {
        return this.delete(`/api/local/videos/${videoId}`);
    }

    // ==================== 下载任务API ====================

    // 获取任务列表
    async getTasks() {
        return this.get('/api/tasks');
    }

    // 创建下载任务
    async createDownloadTask(videoIds) {
        const data = Array.isArray(videoIds) ? { video_ids: videoIds } : { video_ids: [videoIds] };
        return this.post('/api/tasks', data);
    }

    // 获取任务详情
    async getTask(taskId) {
        return this.get(`/api/tasks/${taskId}`);
    }

    // 取消任务
    async cancelTask(taskId) {
        return this.delete(`/api/tasks/${taskId}`);
    }

    // 重试任务
    async retryTask(taskId) {
        return this.post(`/api/tasks/${taskId}/retry`);
    }

    // 获取任务状态统计
    async getTaskStatus() {
        return this.get('/api/tasks/status');
    }

    // ==================== 同步API ====================

    // 获取同步状态
    async getSyncStatus() {
        return this.get('/api/sync/status');
    }

    // 触发同步
    async triggerSync() {
        return this.post('/api/sync/trigger');
    }

    // 获取同步差异
    async getSyncDiff() {
        return this.get('/api/sync/diff');
    }

    // ==================== 系统信息API ====================

    // 获取系统信息
    async getSystemInfo() {
        return this.get('/api/system/info');
    }

    // 健康检查
    async healthCheck() {
        return this.get('/api/system/health');
    }
}

// API响应处理器
class APIResponseHandler {
    static handleSuccess(data, message = '操作成功') {
        if (message) {
            NotificationManager.success(message);
        }
        return data;
    }

    static handleError(error, defaultMessage = '操作失败') {
        const message = error.message || defaultMessage;
        NotificationManager.error(message);
        console.error('API Error:', error);
        throw error;
    }
}

// API加载状态管理
class APILoadingManager {
    constructor() {
        this.loadingStates = new Set();
        this.loadingElement = document.getElementById('loading');
    }

    start(key) {
        this.loadingStates.add(key);
        this.updateLoadingState();
    }

    stop(key) {
        this.loadingStates.delete(key);
        this.updateLoadingState();
    }

    updateLoadingState() {
        if (this.loadingElement) {
            if (this.loadingStates.size > 0) {
                this.loadingElement.classList.add('show');
            } else {
                this.loadingElement.classList.remove('show');
            }
        }
    }

    clear() {
        this.loadingStates.clear();
        this.updateLoadingState();
    }
}

// 带加载状态的API包装器
class APIWithLoading {
    constructor() {
        this.api = new API();
        this.loadingManager = new APILoadingManager();
    }

    async withLoading(key, apiCall) {
        this.loadingManager.start(key);
        try {
            const result = await apiCall();
            return result;
        } catch (error) {
            throw error;
        } finally {
            this.loadingManager.stop(key);
        }
    }

    // 云端视频API（带加载状态）
    async getCloudVideos(params = {}) {
        return this.withLoading('cloud-videos', () => this.api.getCloudVideos(params));
    }

    async getCloudVideoInfo(videoId) {
        return this.withLoading('cloud-video-info', () => this.api.getCloudVideoInfo(videoId));
    }

    // 本地视频API（带加载状态）
    async getLocalVideos(params = {}) {
        return this.withLoading('local-videos', () => this.api.getLocalVideos(params));
    }

    async getLocalVideoInfo(videoId) {
        return this.withLoading('local-video-info', () => this.api.getLocalVideoInfo(videoId));
    }

    async deleteLocalVideo(videoId) {
        return this.withLoading('delete-video', () => this.api.deleteLocalVideo(videoId));
    }

    // 任务API（带加载状态）
    async getTasks() {
        return this.withLoading('tasks', () => this.api.getTasks());
    }

    async createDownloadTask(videoIds) {
        return this.withLoading('create-task', () => this.api.createDownloadTask(videoIds));
    }

    async getTask(taskId) {
        return this.withLoading('task-info', () => this.api.getTask(taskId));
    }

    async cancelTask(taskId) {
        return this.withLoading('cancel-task', () => this.api.cancelTask(taskId));
    }

    async retryTask(taskId) {
        return this.withLoading('retry-task', () => this.api.retryTask(taskId));
    }

    async getTaskStatus() {
        return this.api.getTaskStatus(); // 不显示加载状态，因为这个会频繁调用
    }

    // 同步API（带加载状态）
    async getSyncStatus() {
        return this.api.getSyncStatus(); // 不显示加载状态，因为这个会频繁调用
    }

    async triggerSync() {
        return this.withLoading('sync', () => this.api.triggerSync());
    }

    async getSyncDiff() {
        return this.withLoading('sync-diff', () => this.api.getSyncDiff());
    }

    // 系统信息API（带加载状态）
    async getSystemInfo() {
        return this.withLoading('system-info', () => this.api.getSystemInfo());
    }

    async healthCheck() {
        return this.api.healthCheck(); // 不显示加载状态，因为这个会频繁调用
    }
}

// 全局API实例
window.api = new APIWithLoading();
