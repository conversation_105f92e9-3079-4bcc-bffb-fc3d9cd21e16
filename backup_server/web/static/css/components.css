/* 视频卡片组件 */
.video-card {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    position: relative;
}

.video-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.video-card.selected {
    border: 2px solid #007aff;
}

.video-card-header {
    position: relative;
    height: 160px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}

.video-card-header i {
    font-size: 48px;
    opacity: 0.8;
}

.video-card-checkbox {
    position: absolute;
    top: 12px;
    left: 12px;
    z-index: 10;
}

.video-card-status {
    position: absolute;
    top: 12px;
    right: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
}

.video-card-status.downloaded {
    background: #34c759;
}

.video-card-status.downloading {
    background: #ff9500;
}

.video-card-status.failed {
    background: #ff3b30;
}

.video-card-body {
    padding: 16px;
}

.video-card-title {
    font-size: 16px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-card-meta {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 12px;
}

.video-card-meta-item {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #86868b;
    gap: 6px;
}

.video-card-meta-item i {
    width: 14px;
    text-align: center;
}

.video-card-actions {
    display: flex;
    gap: 8px;
}

.video-card-actions .btn {
    flex: 1;
    justify-content: center;
    font-size: 13px;
    padding: 6px 12px;
}

/* 任务卡片组件 */
.task-card {
    background: #fff;
    border-radius: 12px;
    padding: 16px;
    border-left: 4px solid #d2d2d7;
    transition: all 0.2s ease;
}

.task-card.pending {
    border-left-color: #86868b;
}

.task-card.downloading {
    border-left-color: #007aff;
}

.task-card.completed {
    border-left-color: #34c759;
}

.task-card.failed {
    border-left-color: #ff3b30;
}

.task-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.task-card-title {
    font-size: 16px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 4px;
}

.task-card-subtitle {
    font-size: 13px;
    color: #86868b;
}

.task-card-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.task-card-status.pending {
    background: #f5f5f7;
    color: #86868b;
}

.task-card-status.downloading {
    background: #e3f2fd;
    color: #007aff;
}

.task-card-status.completed {
    background: #e8f5e8;
    color: #34c759;
}

.task-card-status.failed {
    background: #ffebee;
    color: #ff3b30;
}

.task-card-progress {
    margin-bottom: 12px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #f5f5f7;
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    background: #007aff;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-bar-fill.completed {
    background: #34c759;
}

.progress-bar-fill.failed {
    background: #ff3b30;
}

.progress-text {
    font-size: 12px;
    color: #86868b;
    margin-top: 4px;
    display: flex;
    justify-content: space-between;
}

.task-card-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
    margin-bottom: 12px;
    font-size: 13px;
    color: #86868b;
}

.task-card-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.task-card-actions .btn {
    font-size: 12px;
    padding: 4px 8px;
}

/* 模态框组件 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: #fff;
    border-radius: 16px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #f5f5f7;
}

.modal-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1d1d1f;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #86868b;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f5f5f7;
    color: #1d1d1f;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid #f5f5f7;
}

/* 通知组件 */
.notifications {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 10001;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.notification {
    background: #fff;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #007aff;
    max-width: 400px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: #34c759;
}

.notification.warning {
    border-left-color: #ff9500;
}

.notification.error {
    border-left-color: #ff3b30;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    color: #1d1d1f;
}

.notification-close {
    background: none;
    border: none;
    color: #86868b;
    cursor: pointer;
    padding: 0;
    font-size: 16px;
}

.notification-message {
    font-size: 13px;
    color: #86868b;
    line-height: 1.4;
}

/* 空状态组件 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #86868b;
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #1d1d1f;
}

.empty-state p {
    font-size: 14px;
    line-height: 1.5;
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1d1d1f;
    color: #fff;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    margin-bottom: 4px;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* 视频详情样式 */
.video-details {
    font-size: 14px;
}

.detail-section {
    margin-bottom: 24px;
}

.detail-section h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f5f5f7;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.detail-label {
    font-weight: 500;
    color: #86868b;
}

.detail-value {
    color: #1d1d1f;
    font-weight: 500;
}

.detail-value.status-complete {
    color: #34c759;
}

.detail-value.status-partial {
    color: #ff9500;
}

.detail-value.status-error {
    color: #ff3b30;
}

.file-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #f5f5f7;
    border-radius: 8px;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #f5f5f7;
}

.file-item:last-child {
    border-bottom: none;
}

.file-info {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: #1d1d1f;
}

.file-size {
    font-size: 12px;
    color: #86868b;
}

.file-type {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    background: #f5f5f7;
    color: #86868b;
}

.weight-data {
    background: #f5f5f7;
    border-radius: 8px;
    padding: 12px;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 任务卡片错误信息 */
.task-card-error {
    background: #ffebee;
    border: 1px solid #ffcdd2;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 12px;
    font-size: 13px;
    color: #c62828;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.task-card-error i {
    margin-top: 2px;
    flex-shrink: 0;
}
