/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f5f7;
    color: #1d1d1f;
    line-height: 1.6;
}

/* 导航栏 */
.navbar {
    background: #fff;
    border-bottom: 1px solid #e5e5e7;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    height: 100%;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: #1d1d1f;
}

.nav-brand i {
    margin-right: 8px;
    color: #007aff;
}

.nav-menu {
    display: flex;
    margin-left: 40px;
    flex: 1;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    margin-right: 8px;
    text-decoration: none;
    color: #86868b;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.nav-item:hover {
    background-color: #f5f5f7;
    color: #1d1d1f;
}

.nav-item.active {
    background-color: #007aff;
    color: #fff;
}

.nav-item i {
    margin-right: 6px;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #34c759;
    animation: pulse 2s infinite;
}

.status-dot.connecting {
    background-color: #ff9500;
}

.status-dot.error {
    background-color: #ff3b30;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    font-size: 12px;
    color: #86868b;
}

/* 主要内容区域 */
.main-content {
    margin-top: 60px;
    padding: 20px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 页面头部 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e5e7;
}

.page-header h2 {
    font-size: 28px;
    font-weight: 600;
    color: #1d1d1f;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.view-mode-toggle {
    display: flex;
    background: #f5f5f7;
    border-radius: 8px;
    padding: 2px;
}

.view-mode-toggle .btn {
    margin: 0;
    border-radius: 6px;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 6px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #007aff;
    color: #fff;
}

.btn-primary:hover:not(:disabled) {
    background-color: #0056cc;
}

.btn-secondary {
    background-color: #86868b;
    color: #fff;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #6d6d70;
}

.btn-success {
    background-color: #34c759;
    color: #fff;
}

.btn-success:hover:not(:disabled) {
    background-color: #28a745;
}

.btn-warning {
    background-color: #ff9500;
    color: #fff;
}

.btn-warning:hover:not(:disabled) {
    background-color: #e6850e;
}

.btn-outline {
    background-color: transparent;
    color: #007aff;
    border: 1px solid #007aff;
}

.btn-outline:hover:not(:disabled) {
    background-color: #007aff;
    color: #fff;
}

.btn-outline.active {
    background-color: #007aff;
    color: #fff;
}

/* 表单控件 */
.form-input,
.form-select {
    padding: 8px 12px;
    border: 1px solid #d2d2d7;
    border-radius: 6px;
    font-size: 14px;
    background-color: #fff;
    transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: #007aff;
}

/* 复选框 */
.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    gap: 8px;
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #d2d2d7;
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background-color: #007aff;
    border-color: #007aff;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid #fff;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* 视频网格 */
.video-grid-container {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.video-grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f5f5f7;
}

.grid-info,
.storage-info {
    font-size: 14px;
    color: #86868b;
}

.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.pagination .btn {
    min-width: 36px;
    height: 36px;
    padding: 0;
    justify-content: center;
}

/* 任务统计 */
.task-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    background: #fff;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #007aff;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #86868b;
}

/* 任务列表 */
.task-list-container {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.task-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 加载指示器 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    display: none;
}

.loading.show {
    display: flex;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f5f5f7;
    border-top: 4px solid #007aff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 16px;
    color: #86868b;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 16px;
    }
    
    .nav-menu {
        margin-left: 20px;
    }
    
    .nav-item {
        padding: 6px 12px;
        font-size: 14px;
    }
    
    .main-content {
        padding: 16px;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .page-actions {
        width: 100%;
        flex-wrap: wrap;
    }
    
    .video-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
    
    .task-stats {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
}
