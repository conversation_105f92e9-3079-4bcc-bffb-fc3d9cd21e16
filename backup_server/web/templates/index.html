<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- 顶部导航栏 -->
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <i class="fas fa-video"></i>
                    <span>视频备份系统</span>
                </div>
                <div class="nav-menu">
                    <a href="#cloud" class="nav-item active" data-tab="cloud">
                        <i class="fas fa-cloud"></i>
                        云端未同步视频
                    </a>
                    <a href="#local" class="nav-item" data-tab="local">
                        <i class="fas fa-hdd"></i>
                        本地视频
                    </a>
                    <a href="#tasks" class="nav-item" data-tab="tasks">
                        <i class="fas fa-tasks"></i>
                        任务管理
                    </a>
                </div>
                <div class="nav-actions">
                    <button id="sync-btn" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i>
                        同步
                    </button>
                    <div class="status-indicator" id="status-indicator">
                        <span class="status-dot"></span>
                        <span class="status-text">连接中...</span>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 云端未同步视频页面 -->
            <div id="cloud-tab" class="tab-content active">
                <div class="page-header">
                    <h2>云端未同步视频</h2>
                    <div class="page-actions">
                        <div class="filter-group">
                            <select id="cloud-device-filter" class="form-select">
                                <option value="">所有设备</option>
                            </select>
                            <select id="cloud-cat-filter" class="form-select">
                                <option value="">所有猫咪</option>
                            </select>
                            <input type="date" id="cloud-start-date" class="form-input">
                            <input type="date" id="cloud-end-date" class="form-input">
                            <button id="cloud-filter-btn" class="btn btn-secondary">
                                <i class="fas fa-filter"></i>
                                筛选
                            </button>
                        </div>
                        <button id="batch-download-btn" class="btn btn-success" disabled>
                            <i class="fas fa-download"></i>
                            批量下载
                        </button>
                    </div>
                </div>

                <div class="video-grid-container">
                    <div class="video-grid-header">
                        <label class="checkbox-container">
                            <input type="checkbox" id="select-all-cloud">
                            <span class="checkmark"></span>
                            全选
                        </label>
                        <div class="grid-info">
                            <span id="cloud-video-count">0</span> 个视频
                        </div>
                    </div>
                    <div id="cloud-video-grid" class="video-grid">
                        <!-- 云端视频列表将在这里动态生成 -->
                    </div>
                    <div class="pagination" id="cloud-pagination">
                        <!-- 分页控件 -->
                    </div>
                </div>
            </div>

            <!-- 本地视频页面 -->
            <div id="local-tab" class="tab-content">
                <div class="page-header">
                    <h2>本地视频管理</h2>
                    <div class="page-actions">
                        <div class="view-mode-toggle">
                            <button class="btn btn-outline active" data-mode="by_cat">
                                <i class="fas fa-cat"></i>
                                按猫咪查看
                            </button>
                            <button class="btn btn-outline" data-mode="by_device">
                                <i class="fas fa-microchip"></i>
                                按设备查看
                            </button>
                        </div>
                        <div class="filter-group">
                            <select id="local-filter" class="form-select">
                                <option value="">全部</option>
                            </select>
                            <button id="local-filter-btn" class="btn btn-secondary">
                                <i class="fas fa-filter"></i>
                                筛选
                            </button>
                        </div>
                    </div>
                </div>

                <div class="video-grid-container">
                    <div class="video-grid-header">
                        <div class="grid-info">
                            <span id="local-video-count">0</span> 个本地视频
                        </div>
                        <div class="storage-info">
                            <span id="storage-size">0 MB</span> 已使用
                        </div>
                    </div>
                    <div id="local-video-grid" class="video-grid">
                        <!-- 本地视频列表将在这里动态生成 -->
                    </div>
                    <div class="pagination" id="local-pagination">
                        <!-- 分页控件 -->
                    </div>
                </div>
            </div>

            <!-- 任务管理页面 -->
            <div id="tasks-tab" class="tab-content">
                <div class="page-header">
                    <h2>任务管理</h2>
                    <div class="page-actions">
                        <button id="clear-completed-btn" class="btn btn-outline">
                            <i class="fas fa-trash"></i>
                            清理已完成
                        </button>
                        <button id="retry-failed-btn" class="btn btn-warning">
                            <i class="fas fa-redo"></i>
                            重试失败
                        </button>
                    </div>
                </div>

                <div class="task-stats">
                    <div class="stat-card">
                        <div class="stat-number" id="total-tasks">0</div>
                        <div class="stat-label">总任务</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="pending-tasks">0</div>
                        <div class="stat-label">等待中</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="downloading-tasks">0</div>
                        <div class="stat-label">下载中</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="completed-tasks">0</div>
                        <div class="stat-label">已完成</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="failed-tasks">0</div>
                        <div class="stat-label">失败</div>
                    </div>
                </div>

                <div class="task-list-container">
                    <div id="task-list" class="task-list">
                        <!-- 任务列表将在这里动态生成 -->
                    </div>
                </div>
            </div>
        </main>

        <!-- 视频详情模态框 -->
        <div id="video-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modal-title">视频详情</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body" id="modal-body">
                    <!-- 视频详情内容 -->
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary modal-close">关闭</button>
                    <button id="modal-download-btn" class="btn btn-primary">下载</button>
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <div class="loading-text">加载中...</div>
        </div>

        <!-- 通知容器 -->
        <div id="notifications" class="notifications"></div>
    </div>

    <!-- JavaScript -->
    <script src="/static/js/utils.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/components.js"></script>
    <script src="/static/js/app.js"></script>
</body>
</html>
