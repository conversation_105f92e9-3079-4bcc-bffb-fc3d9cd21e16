package main

import (
	"backup-server/internal/api"
	"backup-server/internal/config"
	"backup-server/internal/service"
	"flag"
	"log"
	"os"
)

func main() {
	// 解析命令行参数
	var configPath string
	flag.StringVar(&configPath, "config", "config/config.yaml", "配置文件路径")
	flag.Parse()

	// 加载配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 设置代理环境变量
	if cfg.Proxy.Enabled {
		os.Setenv("HTTP_PROXY", cfg.Proxy.HTTPProxy)
		os.Setenv("HTTPS_PROXY", cfg.Proxy.HTTPSProxy)
		os.Setenv("ALL_PROXY", cfg.Proxy.AllProxy)
		log.Printf("已设置代理: HTTP_PROXY=%s, HTTPS_PROXY=%s, ALL_PROXY=%s", 
			cfg.Proxy.HTTPProxy, cfg.Proxy.HTTPSProxy, cfg.Proxy.AllProxy)
	}

	// 初始化服务
	storageService := service.NewStorageService(cfg)
	videoService := service.NewVideoService(cfg)

	// 初始化存储目录
	if err := storageService.InitStorage(); err != nil {
		log.Fatalf("初始化存储失败: %v", err)
	}

	// 测试云端API连接
	log.Println("测试云端API连接...")
	if err := videoService.TestConnection(); err != nil {
		log.Printf("警告: 云端API连接失败: %v", err)
		log.Println("将在离线模式下运行")
	} else {
		log.Println("云端API连接成功")
	}

	// 创建下载服务和同步服务
	downloadService := service.NewDownloadService(cfg, videoService, storageService)
	syncService := service.NewSyncService(cfg, videoService, storageService, downloadService)

	// 设置路由
	router := api.NewRouter(cfg, videoService, storageService, downloadService, syncService)
	engine := router.SetupRoutes()

	// 启动服务器
	addr := cfg.GetAddr()
	log.Printf("服务器启动在 http://%s", addr)
	log.Printf("Web界面: http://%s", addr)
	log.Printf("API文档: http://%s/api", addr)

	if err := engine.Run(addr); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}
