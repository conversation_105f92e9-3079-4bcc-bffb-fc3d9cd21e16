<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新本地视频界面设计</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        /* 视图选择器样式 */
        .view-selector {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .view-tabs {
            display: flex;
            gap: 5px;
        }
        
        .view-tab {
            padding: 10px 16px;
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .view-tab:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        .view-tab.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .view-controls {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        /* 图标 */
        .icon-list::before { content: "📋"; }
        .icon-device::before { content: "📱"; }
        .icon-cat::before { content: "🐱"; }
        .icon-refresh::before { content: "🔄"; }
        .icon-trash::before { content: "🗑️"; }
        
        /* 视图容器 */
        .local-view {
            display: none;
        }
        
        .local-view.active {
            display: block;
        }
        
        .view-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .view-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 20px;
        }
        
        .stats-summary {
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: #6c757d;
        }
        
        .stats-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        /* 分类容器布局 */
        .category-container {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            min-height: 500px;
        }
        
        .category-sidebar {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }
        
        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .category-header h4 {
            margin: 0;
            color: #495057;
            font-size: 16px;
        }
        
        .category-count {
            background: #3498db;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .category-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .category-item {
            padding: 12px;
            margin-bottom: 8px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .category-item:hover {
            background: #e3f2fd;
            border-color: #3498db;
        }
        
        .category-item.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .category-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .category-item-id {
            font-family: monospace;
            font-size: 12px;
            font-weight: bold;
        }
        
        .category-item-count {
            background: rgba(0,0,0,0.1);
            padding: 1px 6px;
            border-radius: 10px;
            font-size: 11px;
        }
        
        .category-item.active .category-item-count {
            background: rgba(255,255,255,0.3);
        }
        
        .category-item-stats {
            font-size: 11px;
            color: #6c757d;
        }
        
        .category-item.active .category-item-stats {
            color: rgba(255,255,255,0.8);
        }
        
        .category-content {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }
        
        .content-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .content-header h4 {
            margin: 0;
            color: #495057;
            font-size: 16px;
        }
        
        .video-grid {
            display: grid;
            gap: 15px;
        }
        
        .video-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s;
        }
        
        .video-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .placeholder {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-style: italic;
        }
        
        @media (max-width: 1024px) {
            .category-container {
                grid-template-columns: 250px 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .view-selector {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .view-tabs {
                justify-content: center;
            }
            
            .category-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 本地视频管理 - 新界面设计</h1>
        
        <!-- 视图模式选择 -->
        <div class="view-selector">
            <div class="view-tabs">
                <button class="view-tab active" data-view="list" onclick="switchView('list')">
                    <i class="icon-list"></i>列表视图
                </button>
                <button class="view-tab" data-view="device" onclick="switchView('device')">
                    <i class="icon-device"></i>按设备分类
                </button>
                <button class="view-tab" data-view="cat" onclick="switchView('cat')">
                    <i class="icon-cat"></i>按猫分类
                </button>
            </div>
            <div class="view-controls">
                <button class="btn btn-primary">
                    <i class="icon-refresh"></i>刷新
                </button>
                <button class="btn btn-danger">
                    <i class="icon-trash"></i>清理存储
                </button>
            </div>
        </div>

        <!-- 列表视图 -->
        <div id="list-view" class="local-view active">
            <div class="view-header">
                <h3>本地视频列表</h3>
                <div class="stats-summary">
                    <div class="stats-item">
                        <span>📊 总计:</span>
                        <strong>23 个视频</strong>
                    </div>
                    <div class="stats-item">
                        <span>📄 当前页:</span>
                        <strong>1/3</strong>
                    </div>
                </div>
            </div>
            <div class="video-grid">
                <div class="video-card">
                    <strong>视频ID:</strong> 93c2531d58f3ce1b6<br>
                    <strong>设备:</strong> 202502270220f7cbb<br>
                    <strong>猫:</strong> f3ce1b02b40e9477<br>
                    <strong>时间:</strong> 2025/7/30 11:46:35
                </div>
                <div class="video-card">
                    <strong>视频ID:</strong> 93c2531d58f3ce1b7<br>
                    <strong>设备:</strong> 202502270220f7cbb<br>
                    <strong>猫:</strong> f3ce1b02b40ed223<br>
                    <strong>时间:</strong> 2025/7/30 13:13:05
                </div>
            </div>
        </div>

        <!-- 按设备分类视图 -->
        <div id="device-view" class="local-view">
            <div class="view-header">
                <h3>按设备分类</h3>
                <div class="stats-summary">
                    <div class="stats-item">
                        <span>📱 设备数:</span>
                        <strong>2</strong>
                    </div>
                    <div class="stats-item">
                        <span>📊 总视频:</span>
                        <strong>23</strong>
                    </div>
                    <div class="stats-item">
                        <span>💾 总大小:</span>
                        <strong>1.2 GB</strong>
                    </div>
                </div>
            </div>
            
            <div class="category-container">
                <div class="category-sidebar">
                    <div class="category-header">
                        <h4>设备列表</h4>
                        <span class="category-count">2</span>
                    </div>
                    <div class="category-list">
                        <div class="category-item active" onclick="selectItem(this)">
                            <div class="category-item-header">
                                <span class="category-item-id">202502270220f7cbb</span>
                                <span class="category-item-count">15</span>
                            </div>
                            <div class="category-item-stats">
                                <span>大小: 856.3 MB</span>
                            </div>
                        </div>
                        <div class="category-item" onclick="selectItem(this)">
                            <div class="category-item-header">
                                <span class="category-item-id">202502270220f7abc</span>
                                <span class="category-item-count">8</span>
                            </div>
                            <div class="category-item-stats">
                                <span>大小: 423.7 MB</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="category-content">
                    <div class="content-header">
                        <h4>设备 202502270220f7cbb 的视频</h4>
                    </div>
                    <div class="video-grid">
                        <div class="video-card">
                            设备视频内容...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 按猫分类视图 -->
        <div id="cat-view" class="local-view">
            <div class="view-header">
                <h3>按猫分类</h3>
                <div class="stats-summary">
                    <div class="stats-item">
                        <span>🐱 猫数:</span>
                        <strong>3</strong>
                    </div>
                    <div class="stats-item">
                        <span>📊 总视频:</span>
                        <strong>23</strong>
                    </div>
                    <div class="stats-item">
                        <span>💾 总大小:</span>
                        <strong>1.2 GB</strong>
                    </div>
                </div>
            </div>
            
            <div class="category-container">
                <div class="category-sidebar">
                    <div class="category-header">
                        <h4>猫列表</h4>
                        <span class="category-count">3</span>
                    </div>
                    <div class="category-list">
                        <div class="category-item active" onclick="selectItem(this)">
                            <div class="category-item-header">
                                <span class="category-item-id">f3ce1b02b40e9477</span>
                                <span class="category-item-count">12</span>
                            </div>
                            <div class="category-item-stats">
                                <span>大小: 678.9 MB</span>
                            </div>
                        </div>
                        <div class="category-item" onclick="selectItem(this)">
                            <div class="category-item-header">
                                <span class="category-item-id">f3ce1b02b40ed223</span>
                                <span class="category-item-count">8</span>
                            </div>
                            <div class="category-item-stats">
                                <span>大小: 456.1 MB</span>
                            </div>
                        </div>
                        <div class="category-item" onclick="selectItem(this)">
                            <div class="category-item-header">
                                <span class="category-item-id">f3ce1b02b40eabc1</span>
                                <span class="category-item-count">3</span>
                            </div>
                            <div class="category-item-stats">
                                <span>大小: 123.4 MB</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="category-content">
                    <div class="content-header">
                        <h4>猫 f3ce1b02b40e9477 的视频</h4>
                    </div>
                    <div class="video-grid">
                        <div class="video-card">
                            猫视频内容...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchView(viewType) {
            // 更新标签状态
            document.querySelectorAll('.view-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-view="${viewType}"]`).classList.add('active');
            
            // 更新视图显示
            document.querySelectorAll('.local-view').forEach(view => {
                view.classList.remove('active');
            });
            document.getElementById(`${viewType}-view`).classList.add('active');
        }
        
        function selectItem(element) {
            // 移除其他选中状态
            element.parentNode.querySelectorAll('.category-item').forEach(item => {
                item.classList.remove('active');
            });
            // 添加选中状态
            element.classList.add('active');
        }
    </script>
</body>
</html>
