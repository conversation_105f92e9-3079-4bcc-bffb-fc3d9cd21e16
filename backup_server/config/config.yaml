# 备份服务器配置文件

# 服务器配置
server:
  port: 8080
  host: "0.0.0.0"

# 云端API配置
cloud_api:
  base_url: "https://api.caby.care"
  auth_token: "FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"
  timeout: 30s

# 代理配置
proxy:
  http_proxy: "http://127.0.0.1:7897"
  https_proxy: "http://127.0.0.1:7897"
  all_proxy: "socks5://127.0.0.1:7897"
  enabled: true

# 本地存储配置
storage:
  data_dir: "./data"
  videos_dir: "./data/videos"
  metadata_dir: "./data/metadata"
  sync_file: "./data/sync.json"

# 下载配置
download:
  concurrent_downloads: 3      # 并发下载数
  retry_attempts: 3           # 重试次数
  retry_delay: 5s            # 重试延迟
  chunk_size: 1048576        # 下载块大小 (1MB)
  timeout: 300s              # 下载超时

# 同步配置
sync:
  check_interval: 300s       # 同步检查间隔 (5分钟)
  batch_size: 50            # 批量处理大小
  auto_sync: true           # 自动同步

# 日志配置
log:
  level: "info"             # debug, info, warn, error
  file: "./logs/backup.log"
  max_size: 100             # MB
  max_backups: 5
  max_age: 30               # days

# Web界面配置
web:
  static_dir: "./web/static"
  template_dir: "./web/templates"
  upload_max_size: 10485760  # 10MB
