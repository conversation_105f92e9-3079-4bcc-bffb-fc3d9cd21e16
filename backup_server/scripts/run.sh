#!/bin/bash

# 视频备份系统运行脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
cd "$PROJECT_ROOT"

echo -e "${GREEN}视频备份系统 - 开发运行脚本${NC}"
echo "项目目录: $PROJECT_ROOT"

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}错误: 未找到Go环境${NC}"
    exit 1
fi

# 检查配置文件
CONFIG_FILE="config/config.yaml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${RED}错误: 配置文件不存在: $CONFIG_FILE${NC}"
    exit 1
fi

# 设置代理环境变量
echo -e "${YELLOW}设置代理环境变量...${NC}"
export HTTP_PROXY=http://127.0.0.1:7897
export HTTPS_PROXY=http://127.0.0.1:7897
export ALL_PROXY=socks5://127.0.0.1:7897

echo "HTTP_PROXY: $HTTP_PROXY"
echo "HTTPS_PROXY: $HTTPS_PROXY"
echo "ALL_PROXY: $ALL_PROXY"

# 创建必要的目录
echo -e "${YELLOW}创建必要的目录...${NC}"
mkdir -p data/videos/by_cat
mkdir -p data/videos/by_device
mkdir -p data/metadata
mkdir -p logs
mkdir -p web/static
mkdir -p web/templates

# 检查依赖
echo -e "${YELLOW}检查Go模块依赖...${NC}"
go mod tidy

# 运行程序
echo -e "${GREEN}启动视频备份系统...${NC}"
echo -e "${BLUE}访问地址: http://localhost:8080${NC}"
echo -e "${BLUE}API文档: http://localhost:8080/api${NC}"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 使用go run运行程序
exec go run main.go -config "$CONFIG_FILE"
