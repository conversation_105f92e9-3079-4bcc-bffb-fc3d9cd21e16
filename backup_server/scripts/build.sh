#!/bin/bash

# 视频备份系统构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="backup-server"
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME=$(date -u '+%Y-%m-%d_%H:%M:%S')
GO_VERSION=$(go version | awk '{print $3}')

echo -e "${GREEN}开始构建 ${PROJECT_NAME}...${NC}"
echo "版本: ${VERSION}"
echo "构建时间: ${BUILD_TIME}"
echo "Go版本: ${GO_VERSION}"

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}错误: 未找到Go环境${NC}"
    exit 1
fi

# 创建构建目录
BUILD_DIR="build"
mkdir -p ${BUILD_DIR}

# 设置构建标志
LDFLAGS="-X main.Version=${VERSION} -X main.BuildTime=${BUILD_TIME} -X main.GoVersion=${GO_VERSION}"

# 构建不同平台的二进制文件
echo -e "${YELLOW}构建Linux版本...${NC}"
GOOS=linux GOARCH=amd64 go build -ldflags "${LDFLAGS}" -o ${BUILD_DIR}/${PROJECT_NAME}-linux-amd64 .

echo -e "${YELLOW}构建macOS版本...${NC}"
GOOS=darwin GOARCH=amd64 go build -ldflags "${LDFLAGS}" -o ${BUILD_DIR}/${PROJECT_NAME}-darwin-amd64 .

echo -e "${YELLOW}构建Windows版本...${NC}"
GOOS=windows GOARCH=amd64 go build -ldflags "${LDFLAGS}" -o ${BUILD_DIR}/${PROJECT_NAME}-windows-amd64.exe .

# 复制配置文件和Web资源
echo -e "${YELLOW}复制配置文件和资源...${NC}"
cp -r config ${BUILD_DIR}/
cp -r web ${BUILD_DIR}/ 2>/dev/null || echo "web目录不存在，跳过"

# 创建启动脚本
cat > ${BUILD_DIR}/start.sh << 'EOF'
#!/bin/bash

# 检测操作系统
OS=$(uname -s)
ARCH=$(uname -m)

# 选择对应的二进制文件
case "$OS" in
    Linux*)
        BINARY="./backup-server-linux-amd64"
        ;;
    Darwin*)
        BINARY="./backup-server-darwin-amd64"
        ;;
    CYGWIN*|MINGW*|MSYS*)
        BINARY="./backup-server-windows-amd64.exe"
        ;;
    *)
        echo "不支持的操作系统: $OS"
        exit 1
        ;;
esac

# 检查二进制文件是否存在
if [ ! -f "$BINARY" ]; then
    echo "错误: 找不到二进制文件 $BINARY"
    exit 1
fi

# 设置代理环境变量
export HTTP_PROXY=http://127.0.0.1:7897
export HTTPS_PROXY=http://127.0.0.1:7897
export ALL_PROXY=socks5://127.0.0.1:7897

echo "启动视频备份系统..."
echo "代理设置: HTTP_PROXY=$HTTP_PROXY"
echo "访问地址: http://localhost:8080"

# 启动服务
exec "$BINARY" -config config/config.yaml
EOF

chmod +x ${BUILD_DIR}/start.sh

# 创建Windows启动脚本
cat > ${BUILD_DIR}/start.bat << 'EOF'
@echo off
chcp 65001 >nul

echo 启动视频备份系统...

REM 设置代理环境变量
set HTTP_PROXY=http://127.0.0.1:7897
set HTTPS_PROXY=http://127.0.0.1:7897
set ALL_PROXY=socks5://127.0.0.1:7897

echo 代理设置: HTTP_PROXY=%HTTP_PROXY%
echo 访问地址: http://localhost:8080

REM 启动服务
backup-server-windows-amd64.exe -config config\config.yaml

pause
EOF

echo -e "${GREEN}构建完成！${NC}"
echo "构建文件位于: ${BUILD_DIR}/"
echo ""
echo "使用方法:"
echo "  Linux/macOS: cd ${BUILD_DIR} && ./start.sh"
echo "  Windows: cd ${BUILD_DIR} && start.bat"
echo ""
echo "或者直接运行:"
echo "  ./${BUILD_DIR}/${PROJECT_NAME}-linux-amd64 -config ${BUILD_DIR}/config/config.yaml"
