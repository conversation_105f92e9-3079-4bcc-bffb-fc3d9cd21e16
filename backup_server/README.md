# 视频备份系统 (Video Backup Server)

这是一个用于备份猫砂盆视频数据的本地服务器系统，专门用于下载和管理云端的normal_pop类型视频数据。

## 功能特性

- 🎥 **视频备份**: 从云端下载normal_pop类型的视频数据
- 📁 **双重分类**: 支持按猫咪和设备两种方式存储和查看视频
- 🔄 **同步机制**: 智能同步，避免重复下载
- 📊 **任务管理**: 支持批量下载和任务队列管理
- 🌐 **Web界面**: 提供友好的Web管理界面
- 🔧 **代理支持**: 支持HTTP/HTTPS/SOCKS5代理
- 📈 **实时监控**: WebSocket实时更新下载进度

## 项目结构

```
backup_server/
├── main.go                 # 主程序入口
├── go.mod                  # Go模块定义
├── config/                 # 配置文件
│   └── config.yaml        # 主配置文件
├── internal/              # 内部包
│   ├── api/               # API路由层
│   │   └── router.go      # 路由定义
│   ├── config/            # 配置管理
│   │   └── config.go      # 配置结构体
│   ├── model/             # 数据模型
│   │   └── video.go       # 视频相关模型
│   └── service/           # 业务逻辑层
│       ├── video.go       # 视频服务
│       └── storage.go     # 存储服务
├── web/                   # Web前端文件
│   ├── static/            # 静态资源
│   └── templates/         # HTML模板
├── data/                  # 数据存储目录
│   ├── videos/            # 视频文件
│   │   ├── by_cat/        # 按猫咪分类
│   │   └── by_device/     # 按设备分类
│   ├── metadata/          # 元数据
│   └── sync.json          # 同步状态文件
├── logs/                  # 日志文件
└── scripts/               # 脚本文件
    ├── build.sh           # 构建脚本
    └── run.sh             # 运行脚本
```

## 快速开始

### 1. 环境要求

- Go 1.21+
- 网络连接（访问云端API）
- 代理服务器（可选）

### 2. 配置

编辑 `config/config.yaml` 文件，设置：
- 云端API地址和认证Token
- 代理设置（如需要）
- 本地存储路径
- 下载参数

### 3. 运行

#### 开发模式
```bash
./scripts/run.sh
```

#### 生产模式
```bash
# 构建
./scripts/build.sh

# 运行
cd build
./start.sh
```

### 4. 访问

- Web界面: http://localhost:8080
- API文档: http://localhost:8080/api

## API接口

### 云端视频
- `GET /api/cloud/videos` - 获取云端视频列表
- `GET /api/cloud/videos/:id` - 获取云端视频详情

### 本地视频
- `GET /api/local/videos` - 获取本地视频列表
- `GET /api/local/videos/:id` - 获取本地视频详情
- `DELETE /api/local/videos/:id` - 删除本地视频

### 下载任务
- `GET /api/tasks` - 获取任务列表
- `POST /api/tasks` - 创建下载任务
- `GET /api/tasks/:id` - 获取任务详情
- `DELETE /api/tasks/:id` - 取消任务

### 同步管理
- `GET /api/sync/status` - 获取同步状态
- `POST /api/sync/trigger` - 触发同步
- `GET /api/sync/diff` - 获取同步差异

## 配置说明

### 代理配置
```yaml
proxy:
  http_proxy: "http://127.0.0.1:7897"
  https_proxy: "http://127.0.0.1:7897"
  all_proxy: "socks5://127.0.0.1:7897"
  enabled: true
```

### 下载配置
```yaml
download:
  concurrent_downloads: 3    # 并发下载数
  retry_attempts: 3         # 重试次数
  chunk_size: 1048576       # 下载块大小
  timeout: 300s             # 下载超时
```

### 存储配置
```yaml
storage:
  data_dir: "./data"
  videos_dir: "./data/videos"
  metadata_dir: "./data/metadata"
  sync_file: "./data/sync.json"
```

## 数据格式

### 同步状态文件 (sync.json)
```json
{
  "last_sync": "2025-01-24T12:00:00Z",
  "downloaded_videos": {
    "video_id": {
      "video_id": "video_id",
      "device_id": "device123",
      "cat_id": "cat456",
      "local_path": "data/videos/by_cat/cat456/2025-01-24_12-45-07",
      "downloaded_at": "2025-01-24T12:00:00Z",
      "file_count": 150,
      "total_size": 1024000,
      "status": "complete"
    }
  },
  "failed_downloads": [],
  "pending_downloads": []
}
```

## 开发说明

### 添加新功能
1. 在 `internal/model/` 中定义数据模型
2. 在 `internal/service/` 中实现业务逻辑
3. 在 `internal/api/` 中添加API路由
4. 更新配置文件和文档

### 测试
```bash
go test ./...
```

### 构建
```bash
./scripts/build.sh
```

## 故障排除

### 常见问题

1. **连接云端API失败**
   - 检查网络连接
   - 验证认证Token
   - 确认代理设置

2. **下载失败**
   - 检查磁盘空间
   - 验证文件权限
   - 查看日志文件

3. **Web界面无法访问**
   - 检查端口是否被占用
   - 确认防火墙设置
   - 查看服务器日志

### 日志查看
```bash
tail -f logs/backup.log
```

## 许可证

本项目仅供内部开发使用。
