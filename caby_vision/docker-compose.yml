# docker-compose.yml for caby_vision (统一配置，支持GPU和CPU模式)

services:
  caby_vision:
    build:
      context: .
      dockerfile: Dockerfile.triton
      network: host
      args:
        - DEVICE_MODE=${DEVICE_MODE:-auto}
    ports:
      - "127.0.0.1:8000:8000"  # Triton HTTP端口 (仅localhost访问)
      - "8001:8001"  # API代理端口 (保持兼容，允许外部访问)
      - "127.0.0.1:8002:8002"  # Triton指标端口 (仅localhost访问)
    environment:
      - CABY_VISION_MODEL_PATH=${CABY_VISION_MODEL_PATH:-/app/models/caby_vision/best_model_calibrated.pth}
      - FEATURED_MODEL_PATH=${FEATURED_MODEL_PATH:-/app/models/featured/featured_cat_model_quantized.onnx}
      - FEATURED_REFERENCE_PATH=${FEATURED_REFERENCE_PATH:-/app/models/featured/reference_features.json}
      - CABY_VISION_PORT=8001
      - CABY_VISION_HOST=0.0.0.0
      - CABY_VISION_API_KEY=${CABY_VISION_API_KEY:-default_api_key}
      - CABY_VISION_DEVICE=${CABY_VISION_DEVICE:-auto}
      - SHADOW_MODE_ENABLED=${SHADOW_MODE_ENABLED:-true}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-http://localhost:3000,http://localhost:8080}
      # GPU模式环境变量 (仅在GPU模式下生效)
      - NVIDIA_VISIBLE_DEVICES=${NVIDIA_VISIBLE_DEVICES:-all}
      - NVIDIA_DRIVER_CAPABILITIES=${NVIDIA_DRIVER_CAPABILITIES:-compute,utility}
      # CPU模式环境变量 (仅在CPU模式下生效)
      - CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-}
    volumes:
      - ./models:/app/models
      - ~/.cache/huggingface:/cache/huggingface  # 使用主机的HuggingFace缓存
    # GPU配置 (仅在GPU模式下使用，通过profiles控制)
    profiles:
      - gpu
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 30s
      retries: 3
      start_period: 120s

  # CPU模式服务 (无GPU依赖)
  caby_vision_cpu:
    build:
      context: .
      dockerfile: Dockerfile.triton
      network: host
      args:
        - DEVICE_MODE=cpu
    ports:
      - "127.0.0.1:8000:8000"  # Triton HTTP端口 (仅localhost访问)
      - "8001:8001"  # API代理端口 (保持兼容，允许外部访问)
      - "127.0.0.1:8002:8002"  # Triton指标端口 (仅localhost访问)
    environment:
      - CABY_VISION_MODEL_PATH=${CABY_VISION_MODEL_PATH:-/app/models/caby_vision/best_model_calibrated.pth}
      - FEATURED_MODEL_PATH=${FEATURED_MODEL_PATH:-/app/models/featured/featured_cat_model_quantized.onnx}
      - FEATURED_REFERENCE_PATH=${FEATURED_REFERENCE_PATH:-/app/models/featured/reference_features.json}
      - CABY_VISION_PORT=8001
      - CABY_VISION_HOST=0.0.0.0
      - CABY_VISION_API_KEY=${CABY_VISION_API_KEY:-default_api_key}
      - CABY_VISION_DEVICE=cpu
      - SHADOW_MODE_ENABLED=${SHADOW_MODE_ENABLED:-true}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-http://localhost:3000,http://localhost:8080}
      # CPU模式环境变量
      - CUDA_VISIBLE_DEVICES=""  # 禁用CUDA设备
    volumes:
      - ./models:/app/models
      - ~/.cache/huggingface:/cache/huggingface  # 使用主机的HuggingFace缓存
    # CPU模式配置 (通过profiles控制)
    profiles:
      - cpu
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 30s
      retries: 3
      start_period: 120s
