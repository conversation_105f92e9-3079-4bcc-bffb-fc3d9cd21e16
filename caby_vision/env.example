# =====================================
# 🐱 Caby Vision - 猫咪个体识别服务配置
# =====================================
#
# 这是Caby Vision项目的环境变量配置示例文件
# 复制此文件为 .env 并根据你的环境修改相应的值
#
# 使用方法：
# 1. cp env.example .env
# 2. 编辑 .env 文件中的配置项
# 3. 运行 ./scripts/quick_deploy.sh 进行部署
#

# =====================================
# 🚀 核心服务配置
# =====================================

# === API服务配置 ===
# API服务监听地址和端口
CABY_VISION_HOST=0.0.0.0
CABY_VISION_PORT=8001

# API认证密钥 - 生产环境请使用强随机密钥
# 生成方法: openssl rand -hex 32
CABY_VISION_API_KEY=default_api_key

# === 模型配置 ===
# 猫咪个体识别模型文件路径（容器内路径）
# 注意：模型文件不在Git仓库中，需要从训练环境复制
CABY_VISION_MODEL_PATH=/app/models/caby_vision/best_model_calibrated.pth

# 特征模型配置（影子模式）
FEATURED_MODEL_PATH=/app/models/featured/featured_cat_model_quantized.onnx
FEATURED_REFERENCE_PATH=/app/models/featured/reference_features.json

# 设备配置：auto（自动检测）| gpu（强制GPU）| cpu（强制CPU）
CABY_VISION_DEVICE=auto

# 影子模式配置
SHADOW_MODE_ENABLED=true

# =====================================
# 🖥️ Triton Server配置
# =====================================

# Triton Server端口配置
TRITON_HTTP_PORT=8000      # HTTP API端口
TRITON_GRPC_PORT=8001      # gRPC API端口（与API代理端口相同）
TRITON_METRICS_PORT=8002   # 指标监控端口

# =====================================
# 🎮 GPU配置（仅在GPU模式下使用）
# =====================================

# NVIDIA GPU设备配置
# all: 使用所有可用GPU | 0,1: 使用指定GPU | 0: 只使用第一个GPU
NVIDIA_VISIBLE_DEVICES=all

# GPU驱动能力配置
NVIDIA_DRIVER_CAPABILITIES=compute,utility

# CUDA设备可见性（容器内使用）
CUDA_VISIBLE_DEVICES=0

# =====================================
# 🌐 网络和安全配置
# =====================================

# CORS跨域访问控制
# 允许访问API的域名列表，用逗号分隔
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# 信任的代理服务器（可选）
TRUSTED_PROXIES=127.0.0.1

# =====================================
# 🤖 HuggingFace模型缓存配置
# =====================================

# HuggingFace缓存目录（容器内路径）
# 通过Docker volume挂载到主机的 ~/.cache/huggingface
HF_HOME=/cache/huggingface
TRANSFORMERS_CACHE=/cache/huggingface

# 离线模式配置（避免运行时下载模型）
HF_HUB_OFFLINE=1
TRANSFORMERS_OFFLINE=1

# =====================================
# 📁 数据持久化配置
# =====================================

# 模型文件目录（主机路径）
MODELS_DIR=./models

# HuggingFace缓存目录（主机路径）
# 将挂载到容器的 /cache/huggingface
HF_CACHE_DIR=~/.cache/huggingface

# =====================================
# 📊 日志和监控配置
# =====================================

# 日志级别：DEBUG | INFO | WARNING | ERROR
LOG_LEVEL=INFO

# 时区设置
TZ=Asia/Shanghai

# Python输出缓冲配置
PYTHONUNBUFFERED=1

# =====================================
# 🔧 开发和调试配置
# =====================================

# Python路径配置（容器内使用）
PYTHONPATH=/app/triton_service:/app/reid/training:/app

# 调试模式（开发环境使用）
# DEBUG=true

# =====================================
# 🌍 网络代理配置（可选）
# =====================================
# 如果你在防火墙后面需要代理来下载依赖，请取消注释并设置以下环境变量

# HTTP代理配置
# http_proxy=http://127.0.0.1:10808
# https_proxy=http://127.0.0.1:10808
# HTTP_PROXY=http://127.0.0.1:10808
# HTTPS_PROXY=http://127.0.0.1:10808

# 不使用代理的地址列表
# no_proxy=localhost,127.0.0.1,::1
# NO_PROXY=localhost,127.0.0.1,::1

# =====================================
# 🔗 服务集成配置（与其他服务集成时使用）
# =====================================

# 与caby_ai项目集成时的配置
# VISION_HOST=caby_vision        # Docker服务名
# VISION_PORT=8001               # API端口
# VISION_API_KEY=${CABY_VISION_API_KEY}  # 使用相同的API密钥

# =====================================
# 📋 配置说明和注意事项
# =====================================
#
# 重要提示：
# 1. 模型文件不在Git仓库中，需要手动复制到 models/caby_vision/ 目录
# 2. 生产环境请务必修改 CABY_VISION_API_KEY 为强随机密钥
# 3. GPU模式需要安装NVIDIA Docker支持
# 4. HuggingFace缓存会自动挂载，首次运行可能需要下载依赖模型
# 5. 端口配置请确保不与其他服务冲突
#
# 模型文件获取方法：
# - 从reid训练环境复制：cp /path/to/reid/training/models/best_model_calibrated.pth models/caby_vision/
# - 或使用备用模型：cp models/caby_vision/best_model.pth models/caby_vision/best_model_calibrated.pth
#
# 快速部署命令：
# - 自动模式：./scripts/quick_deploy.sh
# - GPU模式：./scripts/quick_deploy.sh --device gpu
# - CPU模式：./scripts/quick_deploy.sh --device cpu
# - 跳过构建：./scripts/quick_deploy.sh --skip-build
#
# 健康检查：
# - curl http://localhost:8001/health
# - python3 scripts/test.py
#
# =====================================