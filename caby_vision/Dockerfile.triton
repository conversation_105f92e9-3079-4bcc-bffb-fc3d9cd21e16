FROM nvcr.io/nvidia/tritonserver:25.05-pyt-python-py3

# 构建参数
ARG DEVICE_MODE=auto

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_VISIBLE_DEVICES=0
ENV TZ=Asia/Shanghai
ENV DEVICE_MODE=${DEVICE_MODE}

# 设置Hugging Face缓存目录 - 使用Docker volume挂载点
ENV HF_HOME=/cache/huggingface
ENV TRANSFORMERS_CACHE=/cache/huggingface
ENV HF_HUB_OFFLINE=1
ENV TRANSFORMERS_OFFLINE=1

# 安装额外的系统依赖 (这一层很少变化，放在前面以便缓存)
USER root
RUN apt-get update && apt-get install -y \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgstreamer1.0-0 \
    libgstreamer-plugins-base1.0-0 \
    wget curl git \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 创建必要的目录 (这些很少变化)
RUN mkdir -p /app/model_repository /cache/huggingface

# 复制requirements文件 (只有依赖变化时才会重新构建后续层)
COPY requirements.triton.txt .

# 安装Python依赖 (这是最耗时的步骤，放在应用代码复制之前)
RUN pip install --no-cache-dir \
    --trusted-host pypi.org \
    --trusted-host pypi.python.org \
    --trusted-host files.pythonhosted.org \
    --disable-pip-version-check \
    -r requirements.triton.txt

# 复制应用代码 (这些经常变化，放在最后)
# 按照变化频率从低到高的顺序复制
COPY reid/ ./reid/
COPY models/ ./models/
COPY triton_service/model_repository/ /app/model_repository/
COPY scripts/ ./scripts/
COPY triton_service/ ./triton_service/

RUN ln -s /usr/bin/python3 /usr/bin/python

# 设置Python路径
ENV PYTHONPATH=/app/triton_service:/app/reid/training:/app

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:8001/v2/health/ready || exit 1

# 暴露端口
EXPOSE 8000 8001 8002

# 启动命令 - 先启动HTTP代理服务器，然后启动Triton
CMD ["python", "triton_service/triton_server.py"]
