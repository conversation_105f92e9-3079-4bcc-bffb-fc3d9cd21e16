# Caby Vision API 文档

## 基本信息

- **基础URL**: `http://your-server.com:8001`
- **服务框架**: LitServe
- **认证方式**: API Key
- **数据格式**: JSON
- **编码**: UTF-8

## 认证

所有API请求都需要在请求头中包含认证信息：

```
X-API-Key: <your-api-key>
Content-Type: application/json
```

## 核心API

### 1. 猫咪个体识别
```
POST /predict
```

**说明**: 识别图像中的猫咪个体，支持特征提取和置信度返回

**请求体:**
```json
{
  "task": "predict",
  "image": "base64_encoded_image_string",
  "return_features": false,
  "return_confidence": true
}
```

**参数说明:**
- `task`: 任务类型，固定为 "predict"
- `image`: Base64编码的图像数据
- `return_features`: 是否返回特征向量 (可选，默认false)
- `return_confidence`: 是否返回置信度 (可选，默认true)

**响应:**
```json
{
  "success": true,
  "error": null,
  "results": {
    "predicted_class": "小白",
    "predicted_id": 0,
    "confidence": 0.95,
    "probabilities": [0.95, 0.03, 0.02],
    "features": [0.1, 0.2, 0.3, ...],
    "processing_time": 0.15
  }
}
```

### 2. 特征提取
```
POST /predict
```

**说明**: 仅提取图像特征向量，不进行分类

**请求体:**
```json
{
  "task": "predict",
  "image": "base64_encoded_image_string",
  "return_features": true,
  "return_confidence": false
}
```

**响应:**
```json
{
  "success": true,
  "error": null,
  "results": {
    "features": [0.1, 0.2, 0.3, ...],
    "feature_dimension": 512,
    "processing_time": 0.12
  }
}
```

### 3. 健康检查
```
POST /predict
```

**说明**: 检查服务和模型状态

**请求体:**
```json
{
  "task": "health"
}
```

**响应:**
```json
{
  "success": true,
  "error": null,
  "results": {
    "status": "healthy",
    "model_loaded": true,
    "device": "cuda:0",
    "classes": ["小白", "小花", "小黑"],
    "num_classes": 3,
    "model_path": "/app/models/caby_vision/best_model_calibrated.pth",
    "timestamp": "2025-01-18T10:00:00Z"
  }
}
```

## 特征识别API (影子模式)

### 1. 特征相似度比较
```
POST /featured/predict
```

**说明**: 使用ONNX模型进行特征提取和相似度比较

**请求体:**
```json
{
  "task": "predict",
  "image": "base64_encoded_image_string",
  "similarity_threshold": 0.8,
  "top_k": 5
}
```

**响应:**
```json
{
  "success": true,
  "error": null,
  "results": {
    "predicted_cat": "小白",
    "confidence": 0.92,
    "similarity_score": 0.88,
    "features": [0.1, 0.2, 0.3, ...],
    "top_matches": [
      {
        "cat_id": "小白",
        "similarity": 0.88,
        "confidence": 0.92
      },
      {
        "cat_id": "小花", 
        "similarity": 0.65,
        "confidence": 0.71
      }
    ],
    "processing_time": 0.18
  }
}
```

### 2. 特征数据库更新
```
POST /featured/update
```

**说明**: 更新参考特征数据库

**请求体:**
```json
{
  "task": "update",
  "cat_id": "小白",
  "features": [0.1, 0.2, 0.3, ...],
  "metadata": {
    "timestamp": "2025-01-18T10:00:00Z",
    "source": "training"
  }
}
```

**响应:**
```json
{
  "success": true,
  "error": null,
  "results": {
    "updated": true,
    "cat_id": "小白",
    "feature_count": 150,
    "database_size": 450
  }
}
```

## 批量处理API

### 1. 批量图像识别
```
POST /batch/predict
```

**说明**: 批量处理多张图像

**请求体:**
```json
{
  "task": "batch_predict",
  "images": [
    {
      "id": "img_001",
      "image": "base64_encoded_image_string_1"
    },
    {
      "id": "img_002", 
      "image": "base64_encoded_image_string_2"
    }
  ],
  "return_features": false,
  "return_confidence": true
}
```

**响应:**
```json
{
  "success": true,
  "error": null,
  "results": {
    "processed_count": 2,
    "results": [
      {
        "id": "img_001",
        "predicted_class": "小白",
        "confidence": 0.95,
        "processing_time": 0.15
      },
      {
        "id": "img_002",
        "predicted_class": "小花",
        "confidence": 0.88,
        "processing_time": 0.14
      }
    ],
    "total_processing_time": 0.29
  }
}
```

## 模型管理API

### 1. 模型信息
```
GET /model/info
```

**说明**: 获取当前加载的模型信息

**响应:**
```json
{
  "success": true,
  "error": null,
  "results": {
    "model_name": "caby_vision_v2.1",
    "model_path": "/app/models/caby_vision/best_model_calibrated.pth",
    "model_size": "45.2MB",
    "input_size": [224, 224],
    "num_classes": 3,
    "classes": ["小白", "小花", "小黑"],
    "device": "cuda:0",
    "loaded_at": "2025-01-18T09:30:00Z"
  }
}
```

### 2. 模型重载
```
POST /model/reload
```

**说明**: 重新加载模型

**请求体:**
```json
{
  "model_path": "/app/models/caby_vision/new_model.pth"
}
```

**响应:**
```json
{
  "success": true,
  "error": null,
  "results": {
    "reloaded": true,
    "model_path": "/app/models/caby_vision/new_model.pth",
    "reload_time": 2.5
  }
}
```

## 错误响应

所有API在出错时都会返回统一的错误格式：

```json
{
  "success": false,
  "error": "Error description",
  "results": {
    "error_code": "ERROR_CODE",
    "details": "Additional error details",
    "timestamp": "2025-01-18T10:00:00Z"
  }
}
```

### 常见错误码

- `INVALID_IMAGE`: 图像数据无效
- `MODEL_NOT_LOADED`: 模型未加载
- `PROCESSING_ERROR`: 图像处理错误
- `FEATURE_EXTRACTION_ERROR`: 特征提取失败
- `INVALID_TASK`: 不支持的任务类型
- `INTERNAL_ERROR`: 内部服务器错误

## 图像要求

- **格式**: JPEG, PNG
- **大小**: 最大10MB
- **分辨率**: 建议224x224或更高
- **编码**: Base64
- **颜色空间**: RGB

## 性能指标

- **单张图像处理时间**: 100-200ms (GPU)
- **批量处理**: 支持最多50张图像
- **并发请求**: 支持最多10个并发
- **内存使用**: 约2GB (GPU模式)

## 版本信息

- **当前版本**: v2.1
- **API版本**: v1
- **模型版本**: caby_vision_v2.1
- **最后更新**: 2025-01-18
