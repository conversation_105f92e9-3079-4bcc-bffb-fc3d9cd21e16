#!/usr/bin/env python3
"""
特征猫咪识别器 (Featured Cat Recognizer)
基于ONNX模型的特征提取和相似度比较
支持影子模式部署
"""

import os
import json
import time
import base64
import io
from typing import Dict, Tuple, List, Optional, Union
import numpy as np
from PIL import Image
import onnxruntime as ort
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler
import logging

logger = logging.getLogger(__name__)

class FeaturedCatRecognizer:
    """特征猫咪识别器 - 影子模式专用"""
    
    def __init__(self,
                 onnx_model_path: str,
                 reference_features_path: str = None,
                 device: str = "auto"):
        """
        初始化特征猫咪识别器

        Args:
            onnx_model_path: ONNX模型路径
            reference_features_path: 参考特征数据库路径
            device: 运行设备 ("auto", "cpu", "cuda")
        """
        self.onnx_model_path = onnx_model_path
        self.reference_features_path = reference_features_path
        self.device = device

        # 类别映射 - 使用通用名称
        self.cat_to_id = {"小白": 0, "小花": 1, "小黑": 2}
        self.id_to_cat = {"0": "小白", "1": "小花", "2": "小黑"}

        # 设置ONNX运行时提供者
        providers = self._get_providers()

        # 加载ONNX模型
        self.session = ort.InferenceSession(onnx_model_path, providers=providers)
        self.input_name = self.session.get_inputs()[0].name

        # 特征标准化器
        self.scaler = StandardScaler()
        self.reference_features = None
        self.reference_labels = None

        # 加载参考特征（如果提供）
        if reference_features_path and os.path.exists(reference_features_path):
            self.load_reference_features(reference_features_path)

        logger.info(f"FeaturedCatRecognizer initialized with device: {device}, providers: {providers}")

    def _get_providers(self) -> List[str]:
        """获取ONNX运行时提供者"""
        if self.device == "cpu":
            return ["CPUExecutionProvider"]
        elif self.device == "cuda":
            if "CUDAExecutionProvider" in ort.get_available_providers():
                return ["CUDAExecutionProvider", "CPUExecutionProvider"]
            else:
                logger.warning("CUDA not available, falling back to CPU")
                return ["CPUExecutionProvider"]
        else:  # auto
            available_providers = ort.get_available_providers()
            if "CUDAExecutionProvider" in available_providers:
                return ["CUDAExecutionProvider", "CPUExecutionProvider"]
            else:
                return ["CPUExecutionProvider"]
    
    def preprocess_image(self, image_input: Union[str, np.ndarray, Image.Image]) -> np.ndarray:
        """
        预处理图像

        Args:
            image_input: 图像输入，可以是文件路径、numpy数组或PIL图像

        Returns:
            预处理后的图像数组
        """
        try:
            # 处理不同类型的输入
            if isinstance(image_input, str):
                # 文件路径
                image = Image.open(image_input).convert('RGB')
            elif isinstance(image_input, np.ndarray):
                # numpy数组
                if image_input.shape[-1] == 3:  # RGB
                    image = Image.fromarray(image_input.astype(np.uint8))
                else:  # BGR
                    image_rgb = image_input[:, :, ::-1]  # BGR to RGB
                    image = Image.fromarray(image_rgb.astype(np.uint8))
            elif isinstance(image_input, Image.Image):
                # PIL图像
                image = image_input.convert('RGB')
            else:
                raise ValueError(f"不支持的图像输入类型: {type(image_input)}")

            # 调整大小
            image = image.resize((224, 224))

            # 转换为numpy数组并归一化
            image_array = np.array(image, dtype=np.float32) / 255.0

            # 标准化
            mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
            std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
            image_array = (image_array - mean) / std

            # 调整维度 (H, W, C) -> (1, C, H, W)
            image_array = np.transpose(image_array, (2, 0, 1))
            image_array = np.expand_dims(image_array, axis=0)

            return image_array

        except Exception as e:
            raise ValueError(f"图像预处理失败: {e}")
    
    def preprocess_image_from_base64(self, image_base64: str) -> np.ndarray:
        """
        从base64字符串预处理图像

        Args:
            image_base64: base64编码的图像字符串

        Returns:
            预处理后的图像数组
        """
        try:
            # 解码base64
            image_bytes = base64.b64decode(image_base64)
            image = Image.open(io.BytesIO(image_bytes))

            # 使用通用预处理方法
            return self.preprocess_image(image)

        except Exception as e:
            raise ValueError(f"Base64图像预处理失败: {e}")

    def extract_features(self, image_input: Union[str, np.ndarray, Image.Image]) -> np.ndarray:
        """
        提取图像特征

        Args:
            image_input: 图像输入，可以是文件路径、numpy数组或PIL图像

        Returns:
            特征向量
        """
        # 预处理图像
        input_data = self.preprocess_image(image_input)

        # ONNX推理
        outputs = self.session.run(None, {self.input_name: input_data})

        # 返回特征向量
        features = outputs[1]  # features是第二个输出
        return features.flatten()

    def extract_features_from_base64(self, image_base64: str) -> np.ndarray:
        """
        从base64字符串提取图像特征

        Args:
            image_base64: base64编码的图像字符串

        Returns:
            特征向量
        """
        # 预处理图像
        input_data = self.preprocess_image_from_base64(image_base64)

        # ONNX推理
        outputs = self.session.run(None, {self.input_name: input_data})

        # 返回特征向量
        features = outputs[1]  # features是第二个输出
        return features.flatten()
    
    def load_reference_features(self, features_path: str):
        """加载参考特征数据库"""
        with open(features_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.reference_features = np.array(data['features'])
        self.reference_labels = np.array(data['labels'])
        
        # 标准化特征
        self.reference_features = self.scaler.fit_transform(self.reference_features)
    
    def predict_with_features(self, image_input: Union[str, np.ndarray, Image.Image], k: int = 7) -> Tuple[str, float, np.ndarray]:
        """
        预测图像类别并返回特征向量

        Args:
            image_input: 图像输入，可以是文件路径、numpy数组或PIL图像
            k: KNN的k值

        Returns:
            (预测的猫咪名称, 置信度, 特征向量)
        """
        # 提取特征
        features = self.extract_features(image_input)
        
        if self.reference_features is not None:
            features_scaled = self.scaler.transform(features.reshape(1, -1))

            # KNN分类
            knn = KNeighborsClassifier(n_neighbors=min(k, len(self.reference_features)), metric='cosine')
            knn.fit(self.reference_features, self.reference_labels)

            # 预测
            pred_label = knn.predict(features_scaled)[0]
            pred_proba = knn.predict_proba(features_scaled)[0]
            confidence = max(pred_proba)

            predicted_cat = self.id_to_cat[str(pred_label)]
        else:
            # 如果没有参考特征，返回默认值
            predicted_cat = "unknown"
            confidence = 0.0

        return predicted_cat, float(confidence), features

    def predict_from_base64_with_features(self, image_base64: str, k: int = 7) -> Tuple[str, float, np.ndarray]:
        """
        从base64字符串预测图像类别并返回特征向量

        Args:
            image_base64: base64编码的图像字符串
            k: KNN的k值

        Returns:
            (预测的猫咪名称, 置信度, 特征向量)
        """
        # 提取特征
        features = self.extract_features_from_base64(image_base64)
        
        if self.reference_features is not None:
            features_scaled = self.scaler.transform(features.reshape(1, -1))

            # KNN分类
            knn = KNeighborsClassifier(n_neighbors=min(k, len(self.reference_features)), metric='cosine')
            knn.fit(self.reference_features, self.reference_labels)

            # 预测
            pred_label = knn.predict(features_scaled)[0]
            pred_proba = knn.predict_proba(features_scaled)[0]
            confidence = max(pred_proba)

            predicted_cat = self.id_to_cat[str(pred_label)]
        else:
            # 如果没有参考特征，返回默认值
            predicted_cat = "unknown"
            confidence = 0.0

        return predicted_cat, float(confidence), features

    def get_feature_info(self) -> Dict:
        """获取特征信息"""
        # 提取一个样本特征来获取维度信息
        dummy_image = np.zeros((224, 224, 3), dtype=np.uint8)
        dummy_features = self.extract_features(dummy_image)
        
        return {
            "feature_dim": len(dummy_features),
            "model_version": "featured_v1.0",
            "device": self.device,
            "providers": self._get_providers()
        }
