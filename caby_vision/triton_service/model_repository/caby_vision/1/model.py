#!/usr/bin/env python3
"""
Triton Caby Vision Model Implementation
基于Triton Inference Server的猫咪个体识别模型
"""

import json
import numpy as np
import triton_python_backend_utils as pb_utils
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(current_dir))

from caby_vision_detector import CabyVisionDetector


class TritonPythonModel:
    """Triton Python Model for Caby Vision Detection"""

    def initialize(self, args):
        """初始化模型"""
        self.model_config = model_config = json.loads(args['model_config'])
        
        # 获取输出配置
        self.output_names = {}
        for output in model_config['output']:
            self.output_names[output['name']] = output
        
        # 模型路径配置 - 从环境变量读取
        import os
        model_path = os.getenv('CABY_VISION_MODEL_PATH', "/app/models/caby_vision/best_model_calibrated.pth")

        # 检查模型文件是否存在
        if not Path(model_path).exists():
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        # 检查设备配置
        device = os.getenv("CABY_VISION_DEVICE", "auto")
        
        # 初始化检测器
        try:
            self.detector = CabyVisionDetector(
                model_path=model_path,
                device=device,
                img_size=224
            )
            print(f"Caby Vision model initialized with: {model_path}")
            print(f"Device configuration: {device}")
            print(f"Model loaded successfully: {self.detector.model is not None}")
        except Exception as e:
            print(f"Failed to initialize Caby Vision model: {e}")
            import traceback
            traceback.print_exc()
            self.detector = None

    def execute(self, requests):
        """执行推理请求"""
        responses = []
        
        for request in requests:
            try:
                # 获取输入
                image_input = pb_utils.get_input_tensor_by_name(request, "IMAGE")
                image_data = image_input.as_numpy()[0].decode('utf-8') if image_input else ""
                
                # 获取可选参数
                return_features_input = pb_utils.get_input_tensor_by_name(request, "RETURN_FEATURES")
                return_features = bool(return_features_input.as_numpy()[0]) if return_features_input else False
                
                return_confidence_input = pb_utils.get_input_tensor_by_name(request, "RETURN_CONFIDENCE")
                return_confidence = bool(return_confidence_input.as_numpy()[0]) if return_confidence_input else True
                
                task_type_input = pb_utils.get_input_tensor_by_name(request, "TASK_TYPE")
                task_type = task_type_input.as_numpy()[0].decode('utf-8') if task_type_input else "predict"
                
                features1_input = pb_utils.get_input_tensor_by_name(request, "FEATURES1")
                features1_str = features1_input.as_numpy()[0].decode('utf-8') if features1_input else ""
                
                features2_input = pb_utils.get_input_tensor_by_name(request, "FEATURES2")
                features2_str = features2_input.as_numpy()[0].decode('utf-8') if features2_input else ""
                
                # 执行推理
                if self.detector is None:
                    result = {
                        "error": "Model not initialized",
                        "success": False
                    }
                else:
                    if task_type == "predict":
                        result = self.detector.predict_from_base64(
                            image_data,
                            return_features=return_features,
                            return_confidence=return_confidence
                        )
                        
                    elif task_type == "features":
                        import base64
                        import cv2
                        from PIL import Image
                        import io
                        
                        try:
                            # 解码图像
                            image_bytes = base64.b64decode(image_data)
                            image_pil = Image.open(io.BytesIO(image_bytes))
                            image_np = np.array(image_pil)
                            
                            # 转换为BGR格式
                            if len(image_np.shape) == 3:
                                if image_np.shape[2] == 3:  # RGB
                                    image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2BGR)
                                elif image_np.shape[2] == 4:  # RGBA
                                    image_np = cv2.cvtColor(image_np, cv2.COLOR_RGBA2BGR)
                            
                            result = self.detector.get_cat_features(image_np)
                            
                        except Exception as e:
                            result = {
                                "error": f"Failed to extract features: {str(e)}",
                                "features": None,
                                "feature_dim": 0,
                                "has_features": False
                            }
                    
                    elif task_type == "similarity":
                        try:
                            features1 = json.loads(features1_str) if features1_str else []
                            features2 = json.loads(features2_str) if features2_str else []
                            
                            if not features1 or not features2:
                                result = {
                                    "error": "Both features1 and features2 are required",
                                    "similarity": 0.0
                                }
                            else:
                                similarity = self.detector.compute_similarity(
                                    np.array(features1),
                                    np.array(features2)
                                )
                                result = {
                                    "similarity": similarity,
                                    "features1_dim": len(features1),
                                    "features2_dim": len(features2)
                                }
                        except Exception as e:
                            result = {
                                "error": f"Failed to compute similarity: {str(e)}",
                                "similarity": 0.0
                            }
                    
                    elif task_type == "health":
                        result = {
                            "status": "healthy",
                            "model_loaded": self.detector.model is not None,
                            "device": str(self.detector.device),
                            "classes": self.detector.class_names,
                            "num_classes": self.detector.num_classes
                        }
                    
                    else:
                        result = {
                            "error": f"Unknown task type: {task_type}",
                            "supported_tasks": ["predict", "features", "similarity", "health"]
                        }
                
                # 准备响应
                success = "error" not in result
                error_message = result.get("error", "")
                results_json = json.dumps(result)
                
                # 创建输出张量
                success_tensor = pb_utils.Tensor("SUCCESS", np.array([success], dtype=bool))
                error_tensor = pb_utils.Tensor("ERROR_MESSAGE", np.array([error_message], dtype=object))
                results_tensor = pb_utils.Tensor("RESULTS", np.array([results_json], dtype=object))
                
                response = pb_utils.InferenceResponse(
                    output_tensors=[success_tensor, error_tensor, results_tensor]
                )
                responses.append(response)
                
            except Exception as e:
                # 处理异常
                error_message = f"Execution error: {str(e)}"
                print(f"Caby Vision model execution error: {error_message}")
                
                success_tensor = pb_utils.Tensor("SUCCESS", np.array([False], dtype=bool))
                error_tensor = pb_utils.Tensor("ERROR_MESSAGE", np.array([error_message], dtype=object))
                results_tensor = pb_utils.Tensor("RESULTS", np.array([json.dumps({})], dtype=object))
                
                response = pb_utils.InferenceResponse(
                    output_tensors=[success_tensor, error_tensor, results_tensor]
                )
                responses.append(response)
        
        return responses

    def finalize(self):
        """清理资源"""
        print("Cat Reid model finalized") 