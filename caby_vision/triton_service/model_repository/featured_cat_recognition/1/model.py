#!/usr/bin/env python3
"""
Triton Featured Cat Recognition Model Implementation
基于Triton Inference Server的特征猫咪识别模型 - 影子模式专用
"""

import json
import numpy as np
import triton_python_backend_utils as pb_utils
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(current_dir))

from featured_cat_recognizer import FeaturedCatRecognizer


class TritonPythonModel:
    """Triton Python Model for Featured Cat Recognition (Shadow Mode)"""

    def initialize(self, args):
        """初始化模型"""
        self.model_config = model_config = json.loads(args['model_config'])
        
        # 获取输出配置
        self.output_names = {}
        for output in model_config['output']:
            self.output_names[output['name']] = output
        
        # 模型路径配置 - 从环境变量读取
        import os
        model_path = os.getenv('FEATURED_MODEL_PATH', "/app/models/featured/featured_cat_model_quantized.onnx")
        reference_path = os.getenv('FEATURED_REFERENCE_PATH', "/app/models/featured/reference_features.json")

        # 检查模型文件是否存在
        if not Path(model_path).exists():
            raise FileNotFoundError(f"Featured model file not found: {model_path}")
        
        # 检查设备配置
        device = os.getenv("CABY_VISION_DEVICE", "auto")
        
        # 初始化特征识别器
        try:
            self.recognizer = FeaturedCatRecognizer(
                onnx_model_path=model_path,
                reference_features_path=reference_path if Path(reference_path).exists() else None,
                device=device
            )
            print(f"Featured Cat Recognition model initialized with: {model_path}")
            print(f"Reference features: {reference_path}")
            print(f"Device configuration: {device}")
            print(f"Model loaded successfully: {self.recognizer.session is not None}")
        except Exception as e:
            print(f"Failed to initialize Featured Cat Recognition model: {e}")
            import traceback
            traceback.print_exc()
            self.recognizer = None

    def execute(self, requests):
        """执行推理请求"""
        responses = []
        
        for request in requests:
            try:
                # 获取输入参数
                image_input = pb_utils.get_input_tensor_by_name(request, "IMAGE")
                image_data = image_input.as_numpy()[0].decode('utf-8')
                
                user_id_input = pb_utils.get_input_tensor_by_name(request, "USER_ID")
                user_id = user_id_input.as_numpy()[0].decode('utf-8') if user_id_input else ""
                
                task_type_input = pb_utils.get_input_tensor_by_name(request, "TASK_TYPE")
                task_type = task_type_input.as_numpy()[0].decode('utf-8') if task_type_input else "extract_features"
                
                # 执行推理
                if self.recognizer is None:
                    result = {
                        "error": "Featured model not initialized",
                        "success": False
                    }
                else:
                    if task_type == "extract_features":
                        # 提取特征向量
                        try:
                            features = self.recognizer.extract_features_from_base64(image_data)
                            feature_info = self.recognizer.get_feature_info()
                            
                            result = {
                                "success": True,
                                "features": features.tolist(),
                                "feature_dim": len(features),
                                "model_version": feature_info["model_version"],
                                "device": feature_info["device"],
                                "user_id": user_id,
                                "timestamp": str(np.datetime64('now'))
                            }
                        except Exception as e:
                            result = {
                                "error": f"Feature extraction failed: {str(e)}",
                                "success": False
                            }
                    
                    elif task_type == "predict_with_features":
                        # 预测并返回特征
                        try:
                            predicted_cat, confidence, features = self.recognizer.predict_from_base64_with_features(image_data)
                            feature_info = self.recognizer.get_feature_info()
                            
                            result = {
                                "success": True,
                                "predicted_cat": predicted_cat,
                                "confidence": float(confidence),
                                "features": features.tolist(),
                                "feature_dim": len(features),
                                "model_version": feature_info["model_version"],
                                "device": feature_info["device"],
                                "user_id": user_id,
                                "timestamp": str(np.datetime64('now'))
                            }
                        except Exception as e:
                            result = {
                                "error": f"Prediction with features failed: {str(e)}",
                                "success": False
                            }
                    
                    else:
                        result = {
                            "error": f"Unsupported task type: {task_type}",
                            "success": False
                        }
                
                # 准备输出
                success_output = pb_utils.Tensor("SUCCESS", np.array([result.get("success", False)], dtype=bool))

                # 对于字符串输出，使用object数组
                error_msg = result.get("error", "")
                error_output = pb_utils.Tensor("ERROR_MESSAGE", np.array([error_msg], dtype=object))

                results_json = json.dumps(result)
                results_output = pb_utils.Tensor("RESULTS", np.array([results_json], dtype=object))

                response = pb_utils.InferenceResponse(
                    output_tensors=[success_output, error_output, results_output]
                )
                responses.append(response)
                
            except Exception as e:
                # 处理异常
                error_msg = f"Request processing failed: {str(e)}"
                print(f"Featured Cat Recognition error: {error_msg}")
                
                success_output = pb_utils.Tensor("SUCCESS", np.array([False], dtype=bool))
                error_output = pb_utils.Tensor("ERROR_MESSAGE", np.array([error_msg], dtype=object))
                results_json = json.dumps({"error": error_msg, "success": False})
                results_output = pb_utils.Tensor("RESULTS", np.array([results_json], dtype=object))

                response = pb_utils.InferenceResponse(
                    output_tensors=[success_output, error_output, results_output]
                )
                responses.append(response)
        
        return responses

    def finalize(self):
        """清理资源"""
        print("Featured Cat Recognition model finalized")
        if hasattr(self, 'recognizer') and self.recognizer:
            # 清理ONNX会话
            if hasattr(self.recognizer, 'session'):
                del self.recognizer.session
            del self.recognizer
