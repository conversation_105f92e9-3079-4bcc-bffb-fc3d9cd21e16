name: "featured_cat_recognition"
backend: "python"
max_batch_size: 0
default_model_filename: "model.py"

input [
  {
    name: "IMAGE"
    data_type: TYPE_STRING
    dims: [1]
  },
  {
    name: "USER_ID"
    data_type: TYPE_STRING
    dims: [1]
    optional: true
  },
  {
    name: "TASK_TYPE"
    data_type: TYPE_STRING
    dims: [1]
    optional: true
  }
]

output [
  {
    name: "SUCCESS"
    data_type: TYPE_BOOL
    dims: [1]
  },
  {
    name: "ERROR_MESSAGE"
    data_type: TYPE_STRING
    dims: [1]
  },
  {
    name: "RESULTS"
    data_type: TYPE_STRING
    dims: [1]
  }
]

instance_group [
  {
    count: 1
    kind: KIND_CPU
  }
]
