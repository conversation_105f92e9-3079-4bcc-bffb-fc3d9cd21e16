#!/usr/bin/env python3
"""
Caby Vision Detection Server
基于LitServe的猫咪个体识别API服务器
"""

import os
import sys
import logging
from pathlib import Path
from typing import Dict, Any, List
import torch
import litserve as ls

# 添加当前目录到PATH以便导入模块
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from caby_vision_detector import CabyVisionDetector

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CabyVisionAPI(ls.LitAPI):
    """Caby Vision API"""
    
    def setup(self, device: str = "auto") -> None:
        """初始化模型和配置"""
        
        # 模型路径配置
        model_path = os.getenv("CABY_VISION_MODEL_PATH", "/app/models/caby_vision/best_model_calibrated.pth")
        
        # 检查模型文件是否存在
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        logger.info(f"Loading model from: {model_path}")
        
        # 初始化猫咪个体识别器
        self.caby_vision_detector = CabyVisionDetector(
            model_path=model_path,
            device=device,
            img_size=224
        )
        
        logger.info("Caby Vision API initialized successfully")
    
    def decode_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """解码请求"""
        return request
    
    def predict(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """执行猫咪个体识别"""
        try:
            # 获取请求参数
            task_type = request.get("task", "predict")
            image_data = request.get("image", "")
            
            # 参数配置
            return_features = request.get("return_features", False)
            return_confidence = request.get("return_confidence", True)
            
            if not image_data and task_type != "health":
                return {
                    "success": False,
                    "error": "No image data provided",
                    "results": {}
                }
            
            # 根据任务类型执行不同的操作
            if task_type == "predict":
                result = self.caby_vision_detector.predict_from_base64(
                    image_data, 
                    return_features=return_features,
                    return_confidence=return_confidence
                )
                

            
            elif task_type == "health":
                result = {
                    "status": "healthy",
                    "model_loaded": self.caby_vision_detector.model is not None,
                    "device": str(self.caby_vision_detector.device),
                    "classes": self.caby_vision_detector.class_names,
                    "num_classes": self.caby_vision_detector.num_classes
                }
            
            else:
                result = {
                    "error": f"Unknown task type: {task_type}",
                    "supported_tasks": ["predict", "health"]
                }
            
            # 检查是否有错误
            if "error" in result:
                return {
                    "success": False,
                    "error": result["error"],
                    "results": result
                }
            
            return {
                "success": True,
                "error": None,
                "results": result
            }
            
        except Exception as e:
            logger.error(f"Prediction error: {str(e)}")
            return {
                "success": False,
                "error": f"Internal server error: {str(e)}",
                "results": {}
            }
    
    def encode_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """编码响应"""
        return response


def create_caby_vision_server(host: str = "0.0.0.0", 
                             port: int = 8001,
                             workers: int = 1,
                             device: str = "auto") -> ls.LitServer:
    """创建Caby Vision服务器"""
    
    api = CabyVisionAPI()
    server = ls.LitServer(
        api, 
        accelerator=device if device != "auto" else "auto",
        workers_per_device=workers
    )
    
    return server


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Caby Vision Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8001, help="Port to bind to")
    parser.add_argument("--workers", type=int, default=1, help="Number of workers")
    parser.add_argument("--device", default="auto", help="Device to use (auto/cpu/cuda)")
    
    args = parser.parse_args()
    
    # 创建并启动服务器
    server = create_caby_vision_server(
        host=args.host,
        port=args.port,
        workers=args.workers,
        device=args.device
    )
    
    logger.info(f"Starting Caby Vision server on {args.host}:{args.port}")
    server.run(port=args.port, host=args.host)


if __name__ == "__main__":
    main() 