# 🐱 Caby Vision - 猫咪个体识别服务

基于深度学习的高精度猫咪个体识别服务，采用NVIDIA Triton Server架构，支持GPU/CPU部署。

## 📋 项目概述

Caby Vision是一个专业的猫咪个体识别服务，基于MegaDescriptor预训练模型，提供高精度的猫咪个体分类和特征提取功能。

### 🎯 核心特性

- ✅ **高精度识别** - 基于MegaDescriptor-T-224预训练模型
- ✅ **置信度评估** - 提供校准后的置信度分数
- ✅ **特征提取** - 支持768维特征向量提取
- ✅ **GPU加速** - 支持NVIDIA GPU加速推理
- ✅ **容器化部署** - 基于NVIDIA Triton Server的Docker部署
- ✅ **RESTful API** - 简洁的HTTP API接口
- ✅ **本地推理** - 无需外部模型下载，完全本地化

## 🏗️ 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client App    │───▶│   FastAPI Proxy  │───▶│  Triton Server  │
│                 │    │   (Port 8001)    │    │   (Port 8000)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   API Gateway    │    │  Caby Vision    │
                       │   Authentication │    │     Model       │
                       └──────────────────┘    └─────────────────┘
```

## 📡 API接口

### 认证方式

所有API请求需要在请求头中包含Bearer Token：

```bash
Authorization: Bearer your_api_key
```

### 1. 健康检查

#### 端点
```
GET /health
```

#### 响应示例
```json
{
  "status": "ok",
  "model_loaded": true,
  "device": "cuda:0",
  "classes": ["cat1", "cat2", "cat3"],
  "num_classes": 3
}
```

### 2. 猫咪个体识别

#### 端点
```
POST /predict
```

#### 请求参数
```json
{
  "image": "base64_encoded_image",
  "return_features": false,
  "return_confidence": true,
  "task": "predict"
}
```

#### 响应示例
```json
{
  "success": true,
  "results": {
    "predicted_cat": "cat1",
    "confidence": 0.8542,
    "class_probabilities": {
      "cat1": 0.8542,
      "cat2": 0.1234,
      "cat3": 0.0224
    },
    "features": [0.123, 0.456, ...],  // 仅当return_features=true时返回
    "feature_dim": 768
  }
}
```

### 3. API使用示例

#### Python示例
```python
import requests
import base64

# 读取图像并编码
with open("cat_image.jpg", "rb") as f:
    image_b64 = base64.b64encode(f.read()).decode()

# 发送请求
response = requests.post(
    "http://localhost:8001/predict",
    headers={"Authorization": "Bearer your_api_key"},
    json={
        "image": image_b64,
        "return_confidence": True
    }
)

result = response.json()
print(f"预测结果: {result['results']['predicted_cat']}")
print(f"置信度: {result['results']['confidence']:.4f}")
```

#### cURL示例
```bash
curl -X POST "http://localhost:8001/predict" \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "image": "base64_encoded_image_data",
    "return_confidence": true
  }'
```

## 🚀 快速部署

### 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- NVIDIA Docker (GPU模式，可选)
- 8GB+ 可用内存

### 1. 克隆项目
```bash
git clone <repository_url>
cd caby_vision
```

### 2. 准备模型文件

将训练好的模型文件放置在正确位置：
```bash
models/caby_vision/best_model_calibrated.pth  # 主模型文件
```

**注意**: 模型文件不在Git仓库中，需要从训练环境复制或通过其他方式获取。

### 3. 一键部署

```bash
# 自动检测GPU/CPU模式并部署
./scripts/quick_deploy.sh

# 强制使用CPU模式
./scripts/quick_deploy.sh --device cpu

# 强制使用GPU模式  
./scripts/quick_deploy.sh --device gpu

# 跳过构建，仅重新部署
./scripts/quick_deploy.sh --skip-build
```

### 4. 验证部署

```bash
# 健康检查
curl http://localhost:8001/health

# 运行综合测试
python3 scripts/test.py

# 查看服务日志
docker-compose logs -f
```

## ⚙️ 配置说明

### 环境变量配置

复制并编辑环境配置文件：
```bash
cp env.example .env
```

主要配置项：
```bash
# API服务配置
CABY_VISION_HOST=0.0.0.0
CABY_VISION_PORT=8001
CABY_VISION_API_KEY=your_secure_api_key

# 模型配置
CABY_VISION_MODEL_PATH=/app/models/caby_vision/best_model_calibrated.pth
CABY_VISION_DEVICE=auto  # auto|gpu|cpu

# GPU配置
NVIDIA_VISIBLE_DEVICES=all
CUDA_VISIBLE_DEVICES=0

# CORS配置
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
```

### 端口配置

| 服务 | 端口 | 说明 |
|-----|------|------|
| API代理 | 8001 | 主要API接口 |
| Triton HTTP | 8000 | Triton Server HTTP接口 |
| Triton指标 | 8002 | Triton Server指标接口 |

## 🔧 高级部署

### 手动Docker部署

```bash
# GPU模式部署
COMPOSE_PROFILES=gpu docker-compose build caby_vision
COMPOSE_PROFILES=gpu docker-compose up -d caby_vision

# CPU模式部署
COMPOSE_PROFILES=cpu docker-compose build caby_vision_cpu
COMPOSE_PROFILES=cpu docker-compose up -d caby_vision_cpu

# 查看状态
docker-compose ps
```

### CPU模式部署

如果没有GPU或需要CPU部署：
```bash
# 推荐：使用快速部署脚本（自动配置）
./scripts/quick_deploy.sh --device cpu

# 或手动使用统一配置文件
COMPOSE_PROFILES=cpu docker-compose up -d caby_vision_cpu
```

**CPU模式特点：**
- ✅ 无需GPU硬件支持
- ✅ 自动切换到CPU优化的Triton配置
- ✅ 完全兼容的API接口
- ⚠️ 推理速度相对较慢（约为GPU模式的5-10倍时间）

### 生产环境部署

1. **安全配置**
   ```bash
   # 生成强随机API密钥
   export CABY_VISION_API_KEY=$(openssl rand -hex 32)
   
   # 限制CORS来源
   export CORS_ALLOWED_ORIGINS=https://yourdomain.com
   ```

2. **性能优化**
   ```bash
   # 指定GPU设备
   export NVIDIA_VISIBLE_DEVICES=0
   
   # 设置内存限制
   docker-compose up -d --memory=4g
   ```

3. **监控配置**
   ```bash
   # 启用Triton指标
   curl http://localhost:8002/metrics
   ```

## 🧪 测试

### 运行测试套件

```bash
# 运行所有测试
python3 scripts/test.py

# 只运行健康检查
python3 scripts/test.py health

# 使用自定义图片测试
python3 scripts/test.py predict --image /path/to/cat.jpg

# 性能测试
python3 scripts/test.py performance --requests 20 --concurrent 5
```

### 性能基准

| 模式 | 推理时间 | 内存占用 | 吞吐量 |
|-----|---------|---------|--------|
| GPU | ~15-25ms | ~2GB | ~40-60 req/s |
| CPU | ~100-200ms | ~1GB | ~5-10 req/s |

## 🛠️ 故障排除

### 常见问题

1. **模型文件未找到**
   ```bash
   # 检查模型文件路径
   ls -la models/caby_vision/
   
   # 确认环境变量
   echo $CABY_VISION_MODEL_PATH
   ```

2. **GPU不可用**
   ```bash
   # 检查NVIDIA驱动
   nvidia-smi
   
   # 检查Docker GPU支持
   docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi
   ```

3. **服务启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs caby_vision
   
   # 检查端口占用
   netstat -tlnp | grep 8001
   ```

### 日志查看

```bash
# 实时日志
./scripts/logs.sh

# 或直接使用docker-compose
docker-compose logs -f --tail=100
```

## 📚 技术栈

- **深度学习**: PyTorch 2.0+, timm
- **推理服务**: NVIDIA Triton Server
- **API框架**: FastAPI
- **容器化**: Docker, Docker Compose
- **计算机视觉**: OpenCV, Pillow
- **模型**: MegaDescriptor-T-224 (预训练)

## 🔒 安全考虑

- 所有API端点都需要Bearer Token认证
- 支持CORS配置用于跨域访问控制
- 容器内部网络隔离
- 建议在生产环境中使用HTTPS

## 🗂️ 模型管理

### 模型文件结构

```
models/
└── caby_vision/
    ├── best_model_calibrated.pth    # 主模型文件 (推荐)
    └── best_model.pth               # 备用模型文件
```

### 模型获取方式

由于模型文件较大且不在Git仓库中，需要通过以下方式获取：

1. **从训练环境复制**
   ```bash
   # 从reid训练目录复制模型
   cp /path/to/reid/training/models/best_model_calibrated.pth models/caby_vision/
   ```

2. **使用本地训练模型**
   ```bash
   # 如果有本地训练环境，可以直接使用
   cd reid/training
   python quick_predict_calibrated.py --with-confidence
   ```

3. **模型验证**
   ```bash
   # 验证模型文件完整性
   python -c "import torch; print(torch.load('models/caby_vision/best_model_calibrated.pth', map_location='cpu').keys())"
   ```

### HuggingFace缓存处理

项目配置为离线模式，避免运行时下载：

```bash
# 环境变量设置
HF_HUB_OFFLINE=1
TRANSFORMERS_OFFLINE=1
HF_HOME=/cache/huggingface
```

如果需要预下载依赖模型：
```bash
# 在有网络的环境中预下载
python -c "import timm; timm.create_model('hf-hub:BVRA/MegaDescriptor-T-224', pretrained=True)"

# 然后将缓存目录挂载到容器
docker run -v ~/.cache/huggingface:/cache/huggingface ...
```

## 🔄 与其他服务集成

### 与caby_ai集成

Caby Vision可以作为caby_ai项目的视觉识别后端：

```go
// Go客户端示例
type VisionClient struct {
    BaseURL string
    APIKey  string
}

func (c *VisionClient) PredictCat(imageBase64 string) (*PredictResponse, error) {
    payload := map[string]interface{}{
        "image": imageBase64,
        "return_confidence": true,
    }

    // 发送请求到 http://caby_vision:8001/predict
    // ...
}
```

### API兼容性

输出格式与reid项目的`quick_predict_calibrated.py --with-confidence`命令完全兼容：

```json
{
  "predicted_cat": "cat1",
  "confidence": 0.8542,
  "class_probabilities": {
    "cat1": 0.8542,
    "cat2": 0.1234,
    "cat3": 0.0224
  }
}
```

## 🚀 开发指南

### 本地开发环境

1. **设置Python环境**
   ```bash
   cd caby_vision
   pip install -r requirements.triton.txt
   ```

2. **运行本地测试**
   ```bash
   # 在reid/training目录下测试模型
   cd reid/training
   python quick_predict_calibrated.py --with-confidence
   ```

3. **调试API服务**
   ```bash
   # 直接运行API服务器
   python triton_service/triton_server.py
   ```

### 添加新的猫咪类别

1. **重新训练模型**
   ```bash
   cd reid/training
   # 添加新的训练数据
   # 重新训练模型
   python train.py --num-classes 4  # 新的类别数量
   ```

2. **更新配置**
   ```bash
   # 更新Triton模型配置
   vim triton_service/model_repository/caby_vision/config.pbtxt
   ```

3. **重新部署**
   ```bash
   ./scripts/quick_deploy.sh
   ```

## 📊 监控和日志

### 服务监控

```bash
# 查看容器状态
docker-compose ps

# 查看资源使用
docker stats

# 查看Triton指标
curl http://localhost:8002/metrics
```

### 日志管理

```bash
# 查看API日志
docker-compose logs caby_vision

# 查看实时日志
./scripts/logs.sh

# 日志轮转配置
# 在docker-compose.yml中配置logging driver
```

## 📄 许可证

本项目遵循相应的开源许可证。详情请查看LICENSE文件。
