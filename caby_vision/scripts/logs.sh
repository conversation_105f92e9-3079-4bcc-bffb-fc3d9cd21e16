#!/bin/bash

# Caby Vision 日志查看脚本
# 用于查看 Docker 容器日志

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 项目目录（docker-compose.yml 所在位置）
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR" || { echo -e "${RED}无法切换到项目目录 $PROJECT_DIR${NC}"; exit 1; }

# 默认值
SERVICE=""
LINES=100
FOLLOW=true
DEVICE_MODE=""

# 显示使用帮助
show_usage() {
    echo -e "${GREEN}🐱 Caby Vision 日志查看器${NC}"
    echo "用法: $0 [选项] [服务]"
    echo
    echo "选项:"
    echo "  -n, --lines NUMBER   显示行数 (默认: 100)"
    echo "  --no-follow          不跟踪日志 (静态输出)"
    echo "  --device MODE        指定设备模式 (auto|gpu|cpu)"
    echo "  -h, --help           显示此帮助信息"
    echo
    echo "服务:"
    echo "  caby_vision          查看 Caby Vision 服务日志 (GPU模式)"
    echo "  caby_vision_cpu      查看 Caby Vision 服务日志 (CPU模式)"
    echo "  vision               别名: caby_vision"
    echo "  cpu                  别名: caby_vision_cpu"
    echo "  all                  查看所有服务日志 (默认)"
    echo
    echo "示例:"
    echo "  $0                          # 查看所有服务日志"
    echo "  $0 vision                   # 查看GPU模式服务日志"
    echo "  $0 cpu                      # 查看CPU模式服务日志"
    echo "  $0 -n 50 vision             # 查看最近50行日志"
    echo "  $0 --no-follow all          # 查看所有日志不跟踪"
    echo "  $0 --device cpu             # 自动选择CPU模式服务"
    echo
    echo "注意:"
    echo "  - 统一使用 docker-compose.yml 文件"
    echo "  - GPU模式服务名: caby_vision"
    echo "  - CPU模式服务名: caby_vision_cpu"
    echo "  - 使用 --device 参数可自动选择对应的服务"
}

# 解析命令行参数
while [[ "$#" -gt 0 ]]; do
    case $1 in
        -n|--lines)
            LINES="$2"
            shift 2
            ;;
        --no-follow)
            FOLLOW=false
            shift
            ;;
        --device)
            DEVICE_MODE="$2"
            if [[ ! "$DEVICE_MODE" =~ ^(auto|gpu|cpu)$ ]]; then
                echo -e "${RED}无效的设备模式: $DEVICE_MODE (支持: auto, gpu, cpu)${NC}"
                exit 1
            fi
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        caby_vision|vision|gpu)
            SERVICE="caby_vision"
            shift
            ;;
        caby_vision_cpu|cpu)
            SERVICE="caby_vision_cpu"
            shift
            ;;
        all)
            SERVICE="all"
            shift
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_usage
            exit 1
            ;;
    esac
done

# 根据设备模式确定服务名称
determine_service() {
    local service_name=""

    if [ "$DEVICE_MODE" = "cpu" ]; then
        service_name="caby_vision_cpu"
    elif [ "$DEVICE_MODE" = "gpu" ]; then
        service_name="caby_vision"
    elif [ "$DEVICE_MODE" = "auto" ]; then
        # 检查哪个服务在运行
        if COMPOSE_PROFILES=cpu docker-compose ps -q caby_vision_cpu 2>/dev/null | grep -q .; then
            service_name="caby_vision_cpu"
        elif COMPOSE_PROFILES=gpu docker-compose ps -q caby_vision 2>/dev/null | grep -q .; then
            service_name="caby_vision"
        else
            echo -e "${YELLOW}未检测到运行中的服务，默认使用GPU模式配置${NC}"
            service_name="caby_vision"
        fi
    fi

    echo "$service_name"
}

# 映射服务名称到docker-compose服务名称
get_docker_service() {
    case $1 in
        caby_vision|vision|gpu) echo "caby_vision" ;;
        caby_vision_cpu|cpu) echo "caby_vision_cpu" ;;
        *) echo "$1" ;;
    esac
}

# 获取适当的compose profiles
get_compose_profile() {
    local service="$1"

    case $service in
        caby_vision|vision|gpu) echo "gpu" ;;
        caby_vision_cpu|cpu) echo "cpu" ;;
        all)
            # 对于all，检查哪个在运行
            if COMPOSE_PROFILES=cpu docker-compose ps -q caby_vision_cpu 2>/dev/null | grep -q .; then
                echo "cpu"
            else
                echo "gpu"
            fi
            ;;
        *) echo "gpu" ;;
    esac
}

# 显示特定服务的日志
show_logs() {
    local service_input="$1"
    local service=$(get_docker_service "$service_input")
    local profile=$(get_compose_profile "$service_input")

    local container_id=$(COMPOSE_PROFILES=$profile docker-compose ps -q "$service" 2>/dev/null)

    if [ -z "$container_id" ]; then
        echo -e "${RED}错误: 服务 '$service_input' 未运行或不存在${NC}"
        echo -e "${YELLOW}提示: 使用以下命令检查服务状态:${NC}"
        echo -e "  ${BLUE}COMPOSE_PROFILES=$profile docker-compose ps${NC}"
        return 1
    fi

    local device_type=""
    if [[ "$service" == *"cpu"* ]]; then
        device_type="${PURPLE}(CPU模式)${NC}"
    else
        device_type="${GREEN}(GPU模式)${NC}"
    fi

    echo -e "${YELLOW}显示 $service_input 日志 ${device_type} ${BLUE}($container_id)${NC}"
    echo -e "${BLUE}使用配置文件: docker-compose.yml (profile: $profile)${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

    if [ "$FOLLOW" = true ]; then
        docker logs -f --tail "$LINES" "$container_id"
    else
        docker logs --tail "$LINES" "$container_id"
    fi
}

# 显示所有服务的日志
show_all_logs() {
    local profile=""

    if [ -n "$DEVICE_MODE" ]; then
        # 用户指定了设备模式
        local service=$(determine_service)
        if [[ "$service" == *"cpu"* ]]; then
            profile="cpu"
        else
            profile="gpu"
        fi
    else
        # 自动检测运行中的服务
        if COMPOSE_PROFILES=cpu docker-compose ps -q caby_vision_cpu 2>/dev/null | grep -q .; then
            profile="cpu"
        elif COMPOSE_PROFILES=gpu docker-compose ps -q caby_vision 2>/dev/null | grep -q .; then
            profile="gpu"
        else
            echo -e "${YELLOW}未检测到运行中的服务，默认使用GPU模式配置${NC}"
            profile="gpu"
        fi
    fi

    local device_type=""
    if [[ "$profile" == "cpu" ]]; then
        device_type="${PURPLE}(CPU模式)${NC}"
    else
        device_type="${GREEN}(GPU模式)${NC}"
    fi

    echo -e "${YELLOW}显示所有服务日志 ${device_type}${NC}"
    echo -e "${BLUE}使用配置文件: docker-compose.yml (profile: $profile)${NC}"
    echo -e "${BLUE}显示最近 $LINES 行${NC}"

    if [ "$FOLLOW" = true ]; then
        echo -e "${BLUE}跟踪模式 (按 Ctrl+C 退出)${NC}"
    fi

    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

    if [ "$FOLLOW" = true ]; then
        COMPOSE_PROFILES=$profile docker-compose logs -f --tail "$LINES"
    else
        COMPOSE_PROFILES=$profile docker-compose logs --tail "$LINES"
    fi
}

# 检查项目文件
if [ ! -f "docker-compose.yml" ]; then
    echo -e "${RED}错误: 未找到 docker-compose.yml 文件${NC}"
    echo -e "${YELLOW}请确保在 caby_vision 项目根目录下运行此脚本${NC}"
    exit 1
fi

# 显示项目信息
echo -e "${GREEN}🐱 Caby Vision 日志查看器${NC}"
echo -e "${BLUE}项目目录: $PROJECT_DIR${NC}"

# 主逻辑
if [ -z "$SERVICE" ] || [ "$SERVICE" = "all" ]; then
    show_all_logs
else
    show_logs "$SERVICE"
fi 