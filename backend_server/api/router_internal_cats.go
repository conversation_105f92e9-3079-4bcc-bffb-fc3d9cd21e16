package api

import (
	"cabycare-server/pkg/cattoilet"

	"github.com/gin-gonic/gin"
)

// RegisterInternalCatsRoutes 注册内部猫咪API路由
// 这些路由专门用于caby_ai访问backend_server的猫咪状态API
func RegisterInternalCatsRoutes(routerGroup *gin.RouterGroup, catHandler *cattoilet.Handler) {
	cats := routerGroup.Group("/internal/cats")
	{
		// 猫咪状态管理路由 - 内部API版本
		// 这些API使用X-User-ID头部来指定用户，而不是从JWT token中获取
		cats.POST("/:cat_id/state", catHandler.MarkCatStateInternal)
		cats.GET("/:cat_id/state", catHandler.GetCatStateInternal)
		cats.DELETE("/:cat_id/state", catHandler.EndCatStateInternal)
	}
}
