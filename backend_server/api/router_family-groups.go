package api

import (
	"cabycare-server/pkg/cattoilet"

	"github.com/gin-gonic/gin"
)

func RegisterFamilyGroupsAuthRoutes(routerGroup *gin.RouterGroup, catHandler *cattoilet.Handler) {
	familyGroups := routerGroup.Group("/family-groups")
	{
		familyGroups.POST("", catHandler.CreateFamilyGroup)            // 创建家庭组
		familyGroups.GET("", catHandler.ListUserFamilyGroups)          // 获取用户的家庭组列表
		familyGroups.GET("/:group_id", catHandler.GetFamilyGroup)      // 获取家庭组详情
		familyGroups.PUT("/:group_id", catHandler.UpdateFamilyGroup)   // 更新家庭组信息
		familyGroups.DELETE("/:group_id", catHandler.DeleteFamilyGroup) // 删除家庭组
		
		familyGroups.GET("/:group_id/members", catHandler.ListFamilyGroupMembers)         // 获取家庭组成员列表
		familyGroups.POST("/:group_id/members", catHandler.AddFamilyGroupMember)          // 添加家庭组成员
		familyGroups.PUT("/:group_id/members/:member_id", catHandler.UpdateFamilyGroupMember) // 更新家庭组成员信息
		familyGroups.DELETE("/:group_id/members/:member_id", catHandler.RemoveFamilyGroupMember) // 移除家庭组成员
		
		familyGroups.GET("/:group_id/devices", catHandler.ListFamilyGroupDevices)         // 获取家庭组设备列表
		familyGroups.POST("/:group_id/devices", catHandler.AddFamilyGroupDevice)          // 添加设备到家庭组
		familyGroups.DELETE("/:group_id/devices/:device_id", catHandler.RemoveFamilyGroupDevice) // 从家庭组移除设备
		
		familyGroups.POST("/:group_id/invitations", catHandler.CreateFamilyGroupInvitation) // 发送邀请
		familyGroups.POST("/:group_id/invitations/email", catHandler.CreateFamilyGroupInvitationByEmail) // 通过邮箱发送邀请

		invitations := routerGroup.Group("/family-groups/invitations")
		{
			invitations.GET("/received", catHandler.ListReceivedFamilyGroupInvitations)      // 获取收到的邀请
			invitations.GET("/sent", catHandler.ListSentFamilyGroupInvitations)              // 获取发送的邀请
			invitations.GET("/:invitation_id", catHandler.GetFamilyGroupInvitation)           // 获取邀请详情
			invitations.PUT("/:invitation_id/process", catHandler.ProcessFamilyGroupInvitation) // 处理邀请
			invitations.DELETE("/:invitation_id/cancel", catHandler.CancelFamilyGroupInvitation) // 取消邀请
		}
}
}
