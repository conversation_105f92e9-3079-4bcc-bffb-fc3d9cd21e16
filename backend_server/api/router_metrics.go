package api

import (
	"cabycare-server/pkg/cattoilet"

	"github.com/gin-gonic/gin"
)

func RegisterMetricsAuthRoutes(routerGroup *gin.RouterGroup, catHandler *cattoilet.Handler) {
	metrics := routerGroup.Group("/metrics")
	{
		// 猫咪统计
		metrics.GET("/cats/:cat_id/daily", catHandler.GetCatDailyMetrics)
		metrics.GET("/cats/:cat_id/monthly", catHandler.GetCatMonthlyMetrics)

		// 设备统计
		// metrics.GET("/devices/:device_id/daily", catHandler.GetDeviceDailyMetrics)
		// metrics.GET("/devices/:device_id/monthly", catHandler.GetDeviceMonthlyMetrics)
	}
}
