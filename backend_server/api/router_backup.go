package api

import (
	"cabycare-server/pkg/cattoilet"

	"github.com/gin-gonic/gin"
)

// RegisterBackupRoutes 注册备份相关路由（使用工厂Token认证）
func RegisterBackupRoutes(routerGroup *gin.RouterGroup, catHandler *cattoilet.Handler) {
	backup := routerGroup.Group("/backup")
	{
		// 获取可备份的视频列表（normal_pop类型）
		backup.GET("/videos", catHandler.ListBackupVideos)
		
		// 获取视频详细信息和下载链接
		backup.GET("/videos/:video_id", catHandler.GetBackupVideoInfo)
	}
}
