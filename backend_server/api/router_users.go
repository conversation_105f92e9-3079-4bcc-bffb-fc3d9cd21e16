package api

import (
	"cabycare-server/pkg/cattoilet"

	"github.com/gin-gonic/gin"
)

func RegisterUserAuthRoutes(routerGroup *gin.RouterGroup, catHandler *cattoilet.Handler) {
	users := routerGroup.Group("/users")
	{
		users.GET("/by-username", catHandler.GetUserByUsername)
		users.GET("/:user_id", catHandler.GetUserByID)
		users.GET("", catHandler.ListUsers)
		users.GET("/:user_id/devices", catHandler.ListUserDevices)
		users.GET("/:user_id/profile", catHandler.GetUserProfile)
		users.PUT("/:user_id/profile", catHandler.UpdateUserProfile)
		users.GET("/:user_id/settings", catHandler.GetUserSettings)
		users.PUT("/:user_id/settings", catHandler.UpdateUserSettings)
		users.GET("/:user_id/notifications", catHandler.ListUserNotifications)
	}
}
