package api

import (
	"cabycare-server/pkg/notification"

	"github.com/gin-gonic/gin"
)

func RegisterNotificationsAuthRoutes(routerGroup *gin.RouterGroup, notificationHandler *notification.Handler) {
	notifications := routerGroup.Group("/notifications")
	{
		notifications.GET("/settings", notificationHandler.GetSettings)
		notifications.PUT("/settings", notificationHandler.UpdateSettings)
		notifications.POST("", notificationHandler.CreateNotification)
		notifications.GET("", notificationHandler.GetUserNotifications)
		notifications.PUT("/:id/read", notificationHandler.MarkAsRead)
	}
}
