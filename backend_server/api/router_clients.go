package api

import (
	"cabycare-server/pkg/cattoilet"

	"github.com/gin-gonic/gin"
)

func RegisterClientsRoutes(routerGroup *gin.RouterGroup, catHandler *cattoilet.Handler) {
	clients := routerGroup.Group("/clients")
	{
		// basic
		clients.POST("/register", catHandler.RegisterClient) // 注册新客户端
		clients.PUT("/:client_id", catHandler.UpdateClient)  // 更新客户端信息
		clients.GET("/:client_id", catHandler.GetClient)     // 获取客户端信息
		clients.GET("", catHandler.ListUserClients)          // 获取用户的所有客户端
		clients.DELETE("/:client_id", catHandler.DeleteClient) // 删除客户端

		// token
		clients.POST("/:client_id/token", catHandler.RegisterClientToken) // 注册/更新推送令牌
		clients.DELETE("/:client_id/token", catHandler.DeleteClientToken) // 删除推送令牌
		clients.GET("/:client_id/token", catHandler.GetClientToken)       // 获取推送令牌信息
		clients.POST("/:client_id/heartbeat", catHandler.ClientHeartbeat)   // 客户端心跳
		clients.PUT("/:client_id/status", catHandler.UpdateClientStatus)    // 更新客户端状态
	}
}
