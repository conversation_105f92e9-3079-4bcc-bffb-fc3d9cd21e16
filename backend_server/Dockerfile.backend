# Stage 1: Build the Go application
FROM golang:1.21-alpine AS builder

WORKDIR /app

# Copy Go module files and download dependencies
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN go build -o cabycare-server .

# Stage 2: Runtime container
FROM ubuntu:22.04

# Set non-interactive installation mode to avoid timezone configuration prompts
ENV DEBIAN_FRONTEND=noninteractive

# Install timezone data and CA certificates properly 
RUN apt-get update && apt-get install -y --no-install-recommends tzdata ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV CONFIG_PATH=/app/config/config.yaml \
    TZ=UTC

# Configure timezone
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

WORKDIR /app
COPY --from=builder /app/cabycare-server /app/

# Expose backend port
EXPOSE 5678

CMD ["./cabycare-server"]
