package algo

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"sync"
	"time"
)

const (
	// 数据中心ID位数
	datacenterBits = 5
	// 工作节点ID位数
	workerBits = 5
	// 序列号位数
	sequenceBits = 12

	// 最大值
	maxDatacenterID = -1 ^ (-1 << datacenterBits)
	maxWorkerID     = -1 ^ (-1 << workerBits)
	maxSequence     = -1 ^ (-1 << sequenceBits)

	// 位移
	workerShift      = sequenceBits
	datacenterShift  = sequenceBits + workerBits
	timestampShift   = sequenceBits + workerBits + datacenterBits

	// 开始时间戳 (2024-01-01 00:00:00 UTC)
	epoch = 1704067200000
)

// Snowflake ID生成器
type Snowflake struct {
	mutex       sync.Mutex
	timestamp   int64
	datacenterID int64
	workerID    int64
	sequence    int64
}

var (
	defaultSnowflake *Snowflake
	once            sync.Once
)

// 初始化默认的雪花算法生成器
func initDefaultSnowflake() {
	once.Do(func() {
			defaultSnowflake = NewSnowflake(1, 1) // 默认使用数据中心1，工作节点1
	})
}

// NewSnowflake 创建一个新的雪花算法生成器
func NewSnowflake(datacenterID, workerID int64) *Snowflake {
	if datacenterID < 0 || datacenterID > maxDatacenterID {
			panic("datacenter ID超出范围")
	}
	if workerID < 0 || workerID > maxWorkerID {
			panic("worker ID超出范围")
	}
	return &Snowflake{
			datacenterID: datacenterID,
			workerID:    workerID,
	}
}

// NextID 生成下一个ID
func (s *Snowflake) NextID() int64 {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	now := time.Now().UnixMilli()
	if now < s.timestamp {
			panic("时钟回拨")
	}

	if now == s.timestamp {
			s.sequence = (s.sequence + 1) & maxSequence
			if s.sequence == 0 {
					// 序列号用完，等待下一毫秒
					for now <= s.timestamp {
							now = time.Now().UnixMilli()
					}
			}
	} else {
			s.sequence = 0
	}

	s.timestamp = now

	id := ((now - epoch) << timestampShift) |
			(s.datacenterID << datacenterShift) |
			(s.workerID << workerShift) |
			s.sequence

	return id
}

func generateHash(input string, length int) string {
	hasher := md5.New()
	hasher.Write([]byte(input))
	hashBytes := hasher.Sum(nil)
	hash := hex.EncodeToString(hashBytes)[:length]
	return hash
}

// 生成各种类型的ID
func GenerateUserID() string {
	initDefaultSnowflake()
	return fmt.Sprintf("%016x", defaultSnowflake.NextID())
}

func GenerateFamilyGroupID(userID string) string {
	userHash := generateHash(userID, 6)
	initDefaultSnowflake()
	return userHash + fmt.Sprintf("%016x", defaultSnowflake.NextID())
}

func GenerateDeviceID() string {
	initDefaultSnowflake()
	return fmt.Sprintf("%s%016x",
			time.Now().Format("20060102"),
			defaultSnowflake.NextID())
}

func GenerateFamilyGroupInvitationID(groupID string) string {
	groupHash := generateHash(groupID, 6)
	initDefaultSnowflake()
	return groupHash + fmt.Sprintf("%016x", defaultSnowflake.NextID())
}

func GenerateVideoID(deviceID, userID string, timestamp int64) (string, error) {
	deviceHash := generateHash(deviceID, 10)
	userHash := generateHash(userID, 6)

	hexTimestamp := fmt.Sprintf("%x", timestamp)
	combined := deviceHash + userHash + hexTimestamp
	return combined, nil
}

func GenerateCatID(userID string) string {
	userHash := generateHash(userID, 6)
	initDefaultSnowflake()

	// combine userHash and snowflakeID
	return userHash + fmt.Sprintf("%016x", defaultSnowflake.NextID())
}
