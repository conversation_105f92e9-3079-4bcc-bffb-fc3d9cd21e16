package factory

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Handler 工厂API处理器
type Handler struct {
	service *Service
}

// NewHandler 创建新的处理器实例
func NewHandler(service *Service) *Handler {
	return &Handler{
		service: service,
	}
}

// ApplySN 申请SN号
// @Summary 申请SN号
// @Description 申请一个或多个SN号用于工厂生产
// @Tags Factory
// @Accept json
// @Produce json
// @Param request body SNApplyRequest true "申请请求"
// @Success 200 {object} SNApplyResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/factory/sn/apply [post]
func (h *Handler) ApplySN(c *gin.Context) {
	var req SNApplyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 获取客户端信息
	clientIP := c.ClientIP()
	userAgent := c.<PERSON>eader("User-Agent")

	// 调用服务申请SN
	response, err := h.service.ApplySN(&req, clientIP, userAgent)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to apply SN",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// QuerySN 查询SN号
// @Summary 查询SN号
// @Description 根据条件查询SN号记录
// @Tags Factory
// @Accept json
// @Produce json
// @Param sn query string false "SN号"
// @Param status query int false "状态"
// @Param device_type query string false "设备类型"
// @Param production_line query string false "生产线"
// @Param batch_number query string false "批次号"
// @Param operator query string false "操作员"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Param page query int false "页码"
// @Param page_size query int false "页大小"
// @Success 200 {object} SNQueryResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/factory/sn/query [get]
func (h *Handler) QuerySN(c *gin.Context) {
	var req SNQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid query parameters",
			"details": err.Error(),
		})
		return
	}

	// 调用服务查询SN
	response, err := h.service.QuerySN(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to query SN",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// GetSNInfo 获取SN详细信息
// @Summary 获取SN详细信息
// @Description 根据SN号获取详细信息
// @Tags Factory
// @Accept json
// @Produce json
// @Param sn path string true "SN号"
// @Success 200 {object} SNRecord
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/factory/sn/{sn} [get]
func (h *Handler) GetSNInfo(c *gin.Context) {
	sn := c.Param("sn")
	if sn == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "SN parameter is required",
		})
		return
	}

	// 验证SN格式
	if err := h.service.ValidateSN(sn); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid SN format",
			"details": err.Error(),
		})
		return
	}

	// 获取SN信息
	record, err := h.service.GetSNInfo(sn)
	if err != nil {
		if err.Error() == "SN "+sn+" not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "SN not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get SN info",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    record,
	})
}

// UpdateSNStatus 更新SN状态
// @Summary 更新SN状态
// @Description 更新SN号的状态（使用或废弃）
// @Tags Factory
// @Accept json
// @Produce json
// @Param request body SNUpdateRequest true "更新请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/factory/sn/update [put]
func (h *Handler) UpdateSNStatus(c *gin.Context) {
	var req SNUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 验证SN格式
	if err := h.service.ValidateSN(req.SN); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid SN format",
			"details": err.Error(),
		})
		return
	}

	// 获取客户端信息
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// 更新SN状态
	err := h.service.UpdateSNStatus(&req, clientIP, userAgent)
	if err != nil {
		if err.Error() == "SN "+req.SN+" not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "SN not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to update SN status",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "SN status updated successfully",
	})
}

// CheckSN 检查SN是否存在
// @Summary 检查SN是否存在
// @Description 检查指定的SN号是否已经存在
// @Tags Factory
// @Accept json
// @Produce json
// @Param sn path string true "SN号"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/factory/sn/{sn}/check [get]
func (h *Handler) CheckSN(c *gin.Context) {
	sn := c.Param("sn")
	if sn == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "SN parameter is required",
		})
		return
	}

	// 验证SN格式
	if err := h.service.ValidateSN(sn); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid SN format",
			"details": err.Error(),
		})
		return
	}

	// 检查SN是否存在
	exists, err := h.service.CheckSNExists(sn)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to check SN",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"sn":     sn,
			"exists": exists,
		},
	})
}

// GetStatistics 获取统计信息
// @Summary 获取统计信息
// @Description 获取SN号的统计信息
// @Tags Factory
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/factory/statistics [get]
func (h *Handler) GetStatistics(c *gin.Context) {
	stats, err := h.service.GetStatistics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get statistics",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}
