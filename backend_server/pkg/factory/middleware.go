package factory

import (
	"cabycare-server/config"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

const (
	AuthHeader   = "Authorization"
	BearerPrefix = "Bearer "
)

// FactoryTokenAuth 工厂API认证中间件
// 用于验证工厂生产线和测试环境的永久token
func FactoryTokenAuth(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader(AuthHeader)
		if authHeader == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Authorization header required",
			})
			return
		}

		if !strings.HasPrefix(authHeader, BearerPrefix) {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Invalid authorization format, must be 'Bearer TOKEN'",
			})
			return
		}

		token := strings.TrimPrefix(authHeader, BearerPrefix)
		if token != cfg.Factory.AuthToken {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Invalid factory token",
			})
			return
		}

		// 认证通过，继续处理请求
		c.Next()
	}
}
