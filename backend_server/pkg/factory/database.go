package factory

import (
	"crypto/rand"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Database 工厂数据库操作结构体
type Database struct {
	db *gorm.DB
}

// NewDatabase 创建新的数据库操作实例
func NewDatabase(db *gorm.DB) *Database {
	return &Database{db: db}
}

// GenerateRandomHex 生成8位随机16进制字符串
func (d *Database) GenerateRandomHex() (string, error) {
	bytes := make([]byte, 4) // 4字节 = 8位16进制字符
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %v", err)
	}
	return fmt.Sprintf("%08X", uint32(bytes[0])<<24|uint32(bytes[1])<<16|uint32(bytes[2])<<8|uint32(bytes[3])), nil
}

// GenerateCompositeSN 生成复合SN号：随机8位16进制 + PCB_SN + 时间戳
func (d *Database) GenerateCompositeSN(pcbSN string) (string, string, int64, error) {
	// 生成8位随机16进制
	randomHex, err := d.GenerateRandomHex()
	if err != nil {
		return "", "", 0, err
	}

	// 获取当前时间戳（毫秒）
	timestamp := time.Now().UnixMilli()

	// 组合SN号：随机16进制_PCB_SN_时间戳
	compositeSN := fmt.Sprintf("%s_%s_%d", randomHex, pcbSN, timestamp)

	return compositeSN, randomHex, timestamp, nil
}

// CreateSNRecord 创建SN记录
func (d *Database) CreateSNRecord(record *SNRecord) error {
	return d.db.Create(record).Error
}

// GetSNRecord 根据SN号获取记录
func (d *Database) GetSNRecord(sn string) (*SNRecord, error) {
	var record SNRecord
	err := d.db.Where("sn = ?", sn).First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

// UpdateSNRecord 更新SN记录
func (d *Database) UpdateSNRecord(record *SNRecord) error {
	return d.db.Save(record).Error
}

// QuerySNRecords 查询SN记录
func (d *Database) QuerySNRecords(req *SNQueryRequest) ([]SNRecord, int64, error) {
	var records []SNRecord
	var total int64

	query := d.db.Model(&SNRecord{})

	// 添加查询条件
	if req.SN != nil && *req.SN != "" {
		query = query.Where("sn = ?", *req.SN)
	}
	if req.PCBSN != nil && *req.PCBSN != "" {
		query = query.Where("pcb_sn = ?", *req.PCBSN)
	}
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}
	if req.DeviceType != nil && *req.DeviceType != "" {
		query = query.Where("device_type = ?", *req.DeviceType)
	}
	if req.ProductionLine != nil && *req.ProductionLine != "" {
		query = query.Where("production_line = ?", *req.ProductionLine)
	}
	if req.BatchNumber != nil && *req.BatchNumber != "" {
		query = query.Where("batch_number = ?", *req.BatchNumber)
	}
	if req.Operator != nil && *req.Operator != "" {
		query = query.Where("operator = ?", *req.Operator)
	}
	if req.StartTime != nil && *req.StartTime != "" {
		startTime, err := time.Parse("2006-01-02 15:04:05", *req.StartTime)
		if err == nil {
			query = query.Where("applied_at >= ?", startTime)
		}
	}
	if req.EndTime != nil && *req.EndTime != "" {
		endTime, err := time.Parse("2006-01-02 15:04:05", *req.EndTime)
		if err == nil {
			query = query.Where("applied_at <= ?", endTime)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("applied_at DESC").Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// CreateSNApplyLog 创建SN申请日志
func (d *Database) CreateSNApplyLog(log *SNApplyLog) error {
	return d.db.Create(log).Error
}

// BatchCreateSNRecords 批量创建SN记录
func (d *Database) BatchCreateSNRecords(records []SNRecord) error {
	return d.db.CreateInBatches(records, 100).Error
}

// BatchCreateSNApplyLogs 批量创建SN申请日志
func (d *Database) BatchCreateSNApplyLogs(logs []SNApplyLog) error {
	return d.db.CreateInBatches(logs, 100).Error
}

// CheckSNExists 检查SN是否已存在
func (d *Database) CheckSNExists(sn string) (bool, error) {
	var count int64
	err := d.db.Model(&SNRecord{}).Where("sn = ?", sn).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetSNStatistics 获取SN统计信息
func (d *Database) GetSNStatistics() (map[string]interface{}, error) {
	var stats map[string]interface{} = make(map[string]interface{})

	// 总数统计
	var totalCount int64
	if err := d.db.Model(&SNRecord{}).Count(&totalCount).Error; err != nil {
		return nil, err
	}
	stats["total_count"] = totalCount

	// 按状态统计
	var statusStats []struct {
		Status int8  `json:"status"`
		Count  int64 `json:"count"`
	}
	if err := d.db.Model(&SNRecord{}).Select("status, count(*) as count").Group("status").Find(&statusStats).Error; err != nil {
		return nil, err
	}
	stats["status_stats"] = statusStats

	// 今日申请数量
	today := time.Now().Format("2006-01-02")
	var todayCount int64
	if err := d.db.Model(&SNRecord{}).Where("DATE(applied_at) = ?", today).Count(&todayCount).Error; err != nil {
		return nil, err
	}
	stats["today_count"] = todayCount

	return stats, nil
}

// CreateProductionBatch 创建生产批次
func (d *Database) CreateProductionBatch(batch *ProductionBatch) error {
	return d.db.Create(batch).Error
}

// GetProductionBatch 根据批次号获取生产批次
func (d *Database) GetProductionBatch(batchNumber string) (*ProductionBatch, error) {
	var batch ProductionBatch
	err := d.db.Where("batch_number = ?", batchNumber).First(&batch).Error
	if err != nil {
		return nil, err
	}
	return &batch, nil
}

// UpdateProductionBatch 更新生产批次
func (d *Database) UpdateProductionBatch(batch *ProductionBatch) error {
	return d.db.Save(batch).Error
}
