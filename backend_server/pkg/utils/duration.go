package utils

import "fmt"

// FormatDuration 将秒数格式化为更易读的时间格式
// 根据时长返回不同格式：
// - 小于1分钟：X秒
// - 1-59分钟：X分X秒 或 X分钟
// - 1小时以上：X小时X分X秒、X小时X分 或 X小时
func FormatDuration(seconds int) string {
    if seconds < 60 {
        return fmt.Sprintf("%d秒", seconds)
    }

    minutes := seconds / 60
    remainingSeconds := seconds % 60

    if minutes < 60 {
        if remainingSeconds == 0 {
            return fmt.Sprintf("%d分钟", minutes)
        }
        return fmt.Sprintf("%d分%d秒", minutes, remainingSeconds)
    }

    hours := minutes / 60
    remainingMinutes := minutes % 60

    if remainingMinutes == 0 {
        return fmt.Sprintf("%d小时", hours)
    }
    if remainingSeconds == 0 {
        return fmt.Sprintf("%d小时%d分", hours, remainingMinutes)
    }
    return fmt.Sprintf("%d小时%d分%d秒", hours, remainingMinutes, remainingSeconds)
}