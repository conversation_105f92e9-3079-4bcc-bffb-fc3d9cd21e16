package auth

import (
	"cabycare-server/pkg/cattoilet"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

type AuthHandler struct {
	LogtoService *LogtoService
	CatToiletService *cattoilet.CatToiletService
}

func NewAuthHandler(logtoService *LogtoService, catToiletService *cattoilet.CatToiletService) *AuthHandler {
	return &AuthHandler{LogtoService: logtoService, CatToiletService: catToiletService}
}

// HandleCallback 处理 Logto 回调
func (h *AuthHandler) HandleCallback(c *gin.Context) {
	code := c.Query("code")
	state := c.Query("state")
	codeVerifier := c.Query("code_verifier")  // 获取PKCE验证参数
	
	log.Printf("Received callback - code: %s, state: %s, code_verifier: %s", code, state, codeVerifier)
	
	if state == "" {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "missing state"})
		return
	}

	// 获取访问令牌，传递code_verifier参数
	tokenResp, err := h.LogtoService.GetAccessToken(code, codeVerifier)
	if err != nil {
		log.Printf("Failed to get access token: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 记录令牌响应
	log.Printf("Token response: %+v", tokenResp)

	// 验证 ID Token
	if err := h.LogtoService.VerifyIDToken(tokenResp.IdToken); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid id_token"})
		return
	}

	// 获取用户信息
	userInfo, err := h.LogtoService.GetUserInfo(tokenResp.AccessToken)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 创建或获取已存在的用户
	user, err := h.CatToiletService.CreateUserWithLogto(userInfo)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"access_token": tokenResp.AccessToken,
		"refresh_token": tokenResp.RefreshToken,
		"id_token": tokenResp.IdToken,
		"expires_in": tokenResp.ExpiresIn,
		"token_type": tokenResp.TokenType,
		"user_id":     user.UserID,
		"logto_id":    user.LogtoID,
		"message":     "Login successful",
	})
}

// GetUserInfo 获取用户信息
func (h *AuthHandler) GetUserInfo(c *gin.Context) {
	token := c.GetHeader("Authorization")
	if token == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "missing token"})
		return
	}

	// 验证令牌
	if err := h.LogtoService.VerifyToken(token); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":  "invalid token",
			"detail": err.Error(),
		})
		return
	}

	// 从 Logto 获取用户信息
	userInfo, err := h.LogtoService.GetUserInfo(token)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "failed to get user info",
			"detail": err.Error(),
		})
		return
	}

	// 获取内部用户信息
	user, err := h.CatToiletService.GetUserByLogtoID(userInfo.Sub)
	if err != nil {
		// 如果用户不存在，可以尝试创建
		user, err = h.CatToiletService.CreateUserWithLogto(userInfo)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "failed to create user",
				"detail": err.Error(),
			})
			return
		}
	}

	// 返回完整的用户信息
	c.JSON(http.StatusOK, gin.H{
		"message": "valid token",
		"user_id": user.UserID,
		"logto_id": user.LogtoID,
		"username": user.Username,
		"email": user.Email,
		"nickname": user.Nickname,
		"logto_user_info": userInfo,
	})
}

// RefreshToken godoc
// @Summary 刷新访问令牌
// @Description 使用 refresh_token 获取新的 access_token
// @Tags 认证
// @Accept json
// @Produce json
// @Param refresh_token body RefreshTokenRequest true "刷新令牌"
// @Success 200 {object} TokenResponse "刷新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "刷新失败"
// @Router /api/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "missing refresh_token"})
		return
	}

	// 使用 refresh token 获取新的访问令牌
	tokenResp, err := h.LogtoService.RefreshAccessToken(req.RefreshToken)
	if err != nil {
		log.Printf("Failed to refresh token: %v", err)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "failed to refresh token", "detail": err.Error()})
		return
	}

	// 尝试获取用户信息，但不让它阻止token返回
	userInfo, err := h.LogtoService.GetUserInfo(tokenResp.AccessToken)
	if err != nil {
		log.Printf("Failed to get user info after token refresh: %v", err)
		// 即使获取用户信息失败，仍然返回新的token
		c.JSON(http.StatusOK, gin.H{
			"access_token": tokenResp.AccessToken,
			"refresh_token": tokenResp.RefreshToken,
			"id_token": tokenResp.IdToken,
			"expires_in": tokenResp.ExpiresIn,
			"token_type": tokenResp.TokenType,
			"message": "Token refreshed successfully (user info unavailable)",
		})
		return
	}

	// 尝试获取内部用户信息
	user, err := h.CatToiletService.GetUserByLogtoID(userInfo.Sub)
	if err != nil {
		log.Printf("Failed to get internal user info: %v", err)
		// 仍然返回token，只是没有用户详细信息
		c.JSON(http.StatusOK, gin.H{
			"access_token": tokenResp.AccessToken,
			"refresh_token": tokenResp.RefreshToken,
			"id_token": tokenResp.IdToken,
			"expires_in": tokenResp.ExpiresIn,
			"token_type": tokenResp.TokenType,
			"message": "Token refreshed successfully (internal user info unavailable)",
		})
		return
	}

	// 完全成功的情况
	c.JSON(http.StatusOK, gin.H{
		"access_token": tokenResp.AccessToken,
		"refresh_token": tokenResp.RefreshToken,
		"id_token": tokenResp.IdToken,
		"expires_in": tokenResp.ExpiresIn,
		"token_type": tokenResp.TokenType,
		"user_id": user.UserID,
		"logto_id": user.LogtoID,
		"message": "Token refreshed successfully",
	})
} 