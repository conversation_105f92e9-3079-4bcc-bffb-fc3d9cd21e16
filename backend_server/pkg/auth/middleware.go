package auth

import (
	"cabycare-server/config"
	"cabycare-server/pkg/cattoilet"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

const (
	AuthHeader   = "Authorization"
	BearerPrefix = "Bearer "
)

func AuthMiddleware(logtoService *LogtoService, catToiletService *cattoilet.CatToiletService) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "missing token"})
			c.Abort()
			return
		}

		// 确保令牌格式正确
		if !strings.HasPrefix(token, "Bearer ") {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid token format"})
			c.Abort()
			return
		}
		token = strings.TrimPrefix(token, "Bearer ")

		// 验证令牌
		userInfo, err := logtoService.GetUserInfo(token)
		if err != nil {
			// 尝试使用 refresh_token
			if refreshToken := c.<PERSON>("X-Refresh-Token"); refreshToken != "" {
				tokenResp, err := logtoService.RefreshAccessToken(refreshToken)
				if err != nil {
					c.JSON(http.StatusUnauthorized, gin.H{"error": "token refresh failed"})
					c.Abort()
					return
				}
				// 使用新令牌重试
				userInfo, err = logtoService.GetUserInfo(tokenResp.AccessToken)
				if err != nil {
					c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid token"})
					c.Abort()
					return
				}
				// 返回新令牌
				c.Header("X-New-Access-Token", tokenResp.AccessToken)
			} else {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid token"})
				c.Abort()
				return
			}
		}

		// 获取内部用户信息
		user, err := catToiletService.GetUserByLogtoID(userInfo.Sub)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "user not found"})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", user.UserID)
		c.Set("logto_id", user.LogtoID)
		c.Set("user_info", userInfo)
		c.Next()
	}
}

func ServiceTokenAuth(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader(AuthHeader)
		if authHeader == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			return
		}

		if !strings.HasPrefix(authHeader, BearerPrefix) {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization format"})
			return
		}

		token := strings.TrimPrefix(authHeader, BearerPrefix)
		if token != cfg.CabyAI.AuthToken {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid service token"})
			return
		}

		c.Next()
	}
}

// CabyAIInternalTokenAuth caby_ai内部token认证中间件
// 用于验证caby_ai访问backend_server内部API的token
func CabyAIInternalTokenAuth(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader(AuthHeader)
		if authHeader == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			return
		}

		if !strings.HasPrefix(authHeader, BearerPrefix) {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization format"})
			return
		}

		token := strings.TrimPrefix(authHeader, BearerPrefix)
		if token != cfg.CabyAI.InternalToken {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid internal token"})
			return
		}

		c.Next()
	}
}
