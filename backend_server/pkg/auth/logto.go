package auth

import (
	"cabycare-server/pkg/types"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"
)

type LogtoService struct {
    config *LogtoConfig
    tokenCache map[string]TokenCacheItem
}

type LogtoConfig struct {
    Endpoint    string
    AppID       string
    AppSecret   string
    APIResource string
    CallbackURI string
}

type TokenResponse struct {
    AccessToken  string `json:"access_token"`
    TokenType    string `json:"token_type"`
    ExpiresIn    int    `json:"expires_in"`
    RefreshToken string `json:"refresh_token"`
    IdToken     string `json:"id_token"`
}

type TokenCacheItem struct {
    AccessToken  string
    RefreshToken string
    ExpiresAt    time.Time
}

func NewLogtoConfig(endpoint, appID, appSecret, apiResource, callbackURI string) *LogtoConfig {
    return &LogtoConfig{
        Endpoint:    endpoint,
        AppID:       appID,
        AppSecret:   appSecret,
        APIResource: apiResource,
        CallbackURI: callbackURI,
    }
}

func NewLogtoService(config *LogtoConfig) *LogtoService {
    return &LogtoService{config: config}
}

// 获取访问令牌
func (s *LogtoService) GetAccessToken(code string, codeVerifier string) (*TokenResponse, error) {
    data := url.Values{}
    data.Set("grant_type", "authorization_code")
    data.Set("code", code)
    data.Set("client_id", s.config.AppID)
    data.Set("redirect_uri", s.config.CallbackURI)
    data.Set("scope", "openid profile email offline_access")
    
    // 当使用PKCE时，通常不需要client_secret（用于公共客户端如移动应用）
    if codeVerifier != "" {
        data.Set("code_verifier", codeVerifier)
        log.Printf("Using PKCE flow with code_verifier")
    } else {
        // 只有在非PKCE流程中才使用client_secret
        data.Set("client_secret", s.config.AppSecret)
        log.Printf("Using client_secret flow")
    }

    tokenURL := fmt.Sprintf("%s/oidc/token", s.config.Endpoint)
    
    log.Printf("Token request URL: %s", tokenURL)
    log.Printf("Token request data: %s", data.Encode())

    resp, err := http.Post(tokenURL, "application/x-www-form-urlencoded", strings.NewReader(data.Encode()))
    if err != nil {
        log.Printf("Token request error: %v", err)
        return nil, err
    }
    defer resp.Body.Close()

    body, err := io.ReadAll(resp.Body)
    if err != nil {
        log.Printf("Failed to read token response: %v", err)
        return nil, err
    }

    // 检查是否有错误响应
    if resp.StatusCode != http.StatusOK {
        log.Printf("Token request failed with status %d: %s", resp.StatusCode, string(body))
        return nil, fmt.Errorf("token request failed with status %d", resp.StatusCode)
    }

    var tokenResp TokenResponse
    if err := json.Unmarshal(body, &tokenResp); err != nil {
        log.Printf("Failed to parse token response: %v", err)
        return nil, err
    }

    // 验证响应中是否包含访问令牌
    if tokenResp.AccessToken == "" {
        return nil, fmt.Errorf("token response does not contain access_token")
    }

    return &tokenResp, nil
}

// 验证访问令牌
func (s *LogtoService) VerifyToken(token string) error {
    userInfoURL := fmt.Sprintf("%s/oidc/me", s.config.Endpoint)
    
    req, err := http.NewRequest("GET", userInfoURL, nil)
    if err != nil {
        return err
    }

    // 确保令牌格式正确
    if !strings.HasPrefix(token, "Bearer ") {
        token = "Bearer " + token
    }
    req.Header.Set("Authorization", token)

    client := &http.Client{
        Timeout: 10 * time.Second,
    }

    resp, err := client.Do(req)
    if err != nil {
        log.Printf("Token verification request failed: %v", err)
        return err
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        io.ReadAll(resp.Body)
        log.Printf("Invalid token: status %d", resp.StatusCode)
        return fmt.Errorf("invalid token: status %d", resp.StatusCode)
    }

    return nil
}

// GetUserInfo 获取用户信息
func (s *LogtoService) GetUserInfo(token string) (*types.LogtoUserInfo, error) {
    userInfoURL := fmt.Sprintf("%s/oidc/me", s.config.Endpoint)

    req, err := http.NewRequest("GET", userInfoURL, nil)
    if err != nil {
        return nil, err
    }

    // 确保令牌格式正确
    if !strings.HasPrefix(token, "Bearer ") {
        token = "Bearer " + token
    }

    req.Header.Set("Authorization", token)

    client := &http.Client{Timeout: 10 * time.Second}
    resp, err := client.Do(req)
    if err != nil {
        log.Printf("User info request failed: %v", err)
        return nil, err
    }
    defer resp.Body.Close()

    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response body: %v", err)
    }

    if resp.StatusCode != http.StatusOK {
        log.Printf("Failed to get user info: status %d", resp.StatusCode)
        return nil, fmt.Errorf("failed to get user info: status %d", resp.StatusCode)
    }

    var userInfo types.LogtoUserInfo
    if err := json.Unmarshal(body, &userInfo); err != nil {
        log.Printf("Failed to decode user info: %v", err)
        return nil, fmt.Errorf("failed to decode user info: %v", err)
    }

    return &userInfo, nil
}

// RefreshAccessToken 刷新访问令牌
func (s *LogtoService) RefreshAccessToken(refreshToken string) (*TokenResponse, error) {
    data := url.Values{}
    data.Set("grant_type", "refresh_token")
    data.Set("refresh_token", refreshToken)
    data.Set("client_id", s.config.AppID)
    data.Set("scope", "openid profile email offline_access")

    tokenURL := fmt.Sprintf("%s/oidc/token", s.config.Endpoint)

    resp, err := http.Post(tokenURL, "application/x-www-form-urlencoded", strings.NewReader(data.Encode()))
    if err != nil {
        log.Printf("Failed to refresh token: %v", err)
        return nil, err
    }
    defer resp.Body.Close()

    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, err
    }

    if resp.StatusCode != http.StatusOK {
        log.Printf("Token refresh failed: status %d", resp.StatusCode)
        log.Printf("Token refresh response body: %s", string(body))
        return nil, fmt.Errorf("token refresh failed with status %d: %s", resp.StatusCode, string(body))
    }

    var tokenResp TokenResponse
    if err := json.Unmarshal(body, &tokenResp); err != nil {
        log.Printf("Failed to parse refresh token response: %v", err)
        return nil, err
    }

    return &tokenResp, nil
}

// VerifyIDToken 验证 ID Token
func (s *LogtoService) VerifyIDToken(idToken string) error {
    // TODO: 实现 JWT 验证
    // 1. 验证签名
    // 2. 验证过期时间
    // 3. 验证发行者
    // 4. 验证受众
    return nil
}