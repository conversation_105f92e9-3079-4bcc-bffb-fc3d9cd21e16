package video

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"cabycare-server/pkg/cattoilet"
	"cabycare-server/pkg/storage"

	"github.com/gin-gonic/gin"
	"github.com/minio/minio-go/v7"
)

type Handler struct {
	service       *storage.StorageService
	toiletService *cattoilet.CatToiletService // Add cattoilet service
}

func NewHandler(service *storage.StorageService, toiletService *cattoilet.CatToiletService) *Handler {
	return &Handler{service: service, toiletService: toiletService}
}

// getScheme 获取请求的协议（http或https）
func (h *Handler) getScheme(c *gin.Context) string {
	// 首先检查X-Forwarded-Proto头，通常由代理服务器设置
	if proto := c.GetHeader("X-Forwarded-Proto"); proto != "" {
		return proto
	}

	// 再检查请求是否通过TLS
	if c.Request.TLS != nil || c.Request.Header.Get("X-Forwarded-SSL") == "on" {
		return "https"
	}

	// 默认为http
	return "http"
}

// 添加一个辅助函数来计算 m3u8 文件的总时长
func (h *Handler) calculateM3U8Duration(bucket, path string) (float64, error) {
	object, err := h.service.GetMinioClient().GetObject(
		context.Background(),
		bucket,
		path,
		minio.GetObjectOptions{},
	)
	if err != nil {
		return 0, err
	}
	defer object.Close()

	content, err := io.ReadAll(object)
	if err != nil {
		return 0, err
	}

	var totalDuration float64
	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		trimmedLine := strings.TrimSpace(line)
		if strings.HasPrefix(trimmedLine, "#EXTINF:") {
			durationStr := strings.TrimPrefix(trimmedLine, "#EXTINF:")
			durationStr = strings.TrimSuffix(durationStr, ",")
			duration, err := strconv.ParseFloat(durationStr, 64)
			if err == nil {
				totalDuration += duration
			}
		}
	}
	return totalDuration, nil
}

// ListVideos 处理获取视频列表的请求
func (h *Handler) ListVideos(c *gin.Context) {
	var req VideoListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Printf("参数绑定错误: %v", err)
		c.JSON(http.StatusBadRequest, []RecordSegment{})
		return
	}

	// 从路径中提取设备ID
	deviceID, err := h.service.ParseDeviceIDFromPath(req.Path)
	if err != nil {
		log.Printf("路径解析错误: %v, path=%s", err, req.Path)
		c.JSON(http.StatusBadRequest, []RecordSegment{})
		return
	}

	device, err := h.toiletService.GetDevice(deviceID)
	if err != nil {
		log.Printf("获取设备信息错误: %v", err)
		c.JSON(http.StatusBadRequest, []RecordSegment{})
		return
	}

	// 确保有可用的时区信息
	location, err := time.LoadLocation(device.Timezone)
	if err != nil {
		log.Printf("解析时区错误: %v，使用UTC", err)
		location = time.UTC
	}

	// 解析开始时间为Unix时间戳
	var startUnixTime int64
	if req.Start != "" {
		startTime, err := time.ParseInLocation("2006-01-02", req.Start, location)
		if err != nil {
			log.Printf("开始时间格式错误: %v", err)
			c.JSON(http.StatusBadRequest, []RecordSegment{})
			return
		}
		startUnixTime = startTime.Unix()
	} else {
		log.Printf("开始时间不能为空")
		c.JSON(http.StatusBadRequest, []RecordSegment{})
		return
	}

	// 解析结束时间为Unix时间戳
	var endUnixTime int64
	if req.End != "" {
		endTime, err := time.ParseInLocation("2006-01-02", req.End, location)
		if err != nil {
			log.Printf("结束时间格式错误: %v", err)
			c.JSON(http.StatusBadRequest, []RecordSegment{})
			return
		}
		// 将结束时间设置为所选日期后48小时减去1秒
		endUnixTime = endTime.Add(48 * time.Hour).Add(-time.Second).Unix()
	} else {
		log.Printf("结束时间不能为空")
		c.JSON(http.StatusBadRequest, []RecordSegment{})
		return
	}

	// 验证时间范围
	if endUnixTime < startUnixTime {
		log.Printf("时间范围错误: 结束时间早于开始时间")
		c.JSON(http.StatusBadRequest, []RecordSegment{})
		return
	}

	// log.Printf("开始时间: %s, 结束时间: %s", time.Unix(startUnixTime, 0).Format("2006-01-02 15:04:05"), time.Unix(endUnixTime, 0).Format("2006-01-02 15:04:05"))

	// 通过优化后的时间戳方式查询视频记录 (只返回status=1的记录)
	records, err := h.toiletService.ListDeviceRecordsByUnixTime(deviceID, startUnixTime, endUnixTime)
	if err != nil {
		log.Printf("获取设备记录错误: %v", err)
		c.JSON(http.StatusInternalServerError, []RecordSegment{})
		return
	}

	// 批量构建视频段信息
	var segments []RecordSegment
	for _, record := range records {
		// 再次验证记录状态（双重保障）
		if record.Status != 1 {
			log.Printf("跳过非正常状态的记录: %s, status=%d", record.VideoID, record.Status)
			continue
		}

		// 计算视频持续时间
		var duration float64
		if record.EndTime != nil {
			duration = float64(*record.EndTime - record.StartTime)
		}

		// 格式化持续时间为字符串
		durationStr := fmt.Sprintf("%.1f", duration)

		// 获取视频URL, videoTime should be in the same timezone as the device
		videoTime := record.StartTime
		// log.Printf("视频时间: %s", videoTime.Format("2006-01-02 15:04:05"))
		videoURL, err := h.service.GetVideoPathWithDuration(location, record.StartTime, duration, deviceID)
		if err != nil {
			log.Printf("获取视频路径错误: %v", err)
			continue
		}

		// 生成缩略图URL
		thumbnailURL := h.service.GenerateThumbnailURL(time.Unix(record.StartTime, 0).In(location), deviceID, h.getScheme(c), c.Request.Host)

		// 构建视频段信息
		var animalID string
		if record.Analysis != nil {
			animalID = record.Analysis.AnimalID
		}

		segment := RecordSegment{
			Start:        videoTime,
			Duration:     durationStr,
			URL:          videoURL,
			WeightLitter: record.WeightLitter,
			WeightCat:    record.WeightCat,
			WeightWaste:  record.WeightWaste,
			ThumbnailURL: thumbnailURL,
			AnimalID:     animalID,
		}
		segments = append(segments, segment)
	}

	// 返回结果
	c.JSON(http.StatusOK, segments)
}

// GetPlaylist 处理获取视频的请求
func (h *Handler) GetPlaylist(c *gin.Context) {
	var req struct {
		Path     string    `form:"path" binding:"required"`
		Start    time.Time `form:"start" binding:"required"`
		Duration string    `form:"duration"`
	}

	if err := c.ShouldBindQuery(&req); err != nil {
		log.Printf("[GetPlaylist] 参数绑定错误: %v, 请求参数: %+v", err, c.Request.URL.Query())
		c.String(http.StatusBadRequest, "参数错误")
		return
	}

	// 从路径中提取设备ID
	deviceID, err := h.service.ParseDeviceIDFromPath(req.Path)
	if err != nil {
		log.Printf("[GetPlaylist] 路径解析错误: %v, path=%s", err, req.Path)
		c.String(http.StatusBadRequest, "路径格式错误")
		return
	}

	// 使用storage service获取播放列表内容
	content, err := h.service.GetPlaylistContent(c.Request.Context(), deviceID, req.Start)
	if err != nil {
		log.Printf("[GetPlaylist] 获取播放列表错误: %v, deviceID=%s", err, deviceID)
		c.String(http.StatusInternalServerError, "获取视频失败")
		return
	}

	// 从时间戳解析出文件夹名称（用于修改ts路径）
	folderName := req.Start.Format("2006-01-02_15-04-05") + "_hls"

	// 修改 ts 文件路径并计算总时长
	lines := strings.Split(string(content), "\n")
	var modifiedLines []string
	var totalDuration float64

	for _, line := range lines {
		trimmedLine := strings.TrimSpace(line)
		if strings.HasPrefix(trimmedLine, "#EXTINF:") {
			// 解析持续时间
			durationStr := strings.TrimPrefix(trimmedLine, "#EXTINF:")
			durationStr = strings.TrimSuffix(durationStr, ",")
			duration, err := strconv.ParseFloat(durationStr, 64)
			if err == nil {
				totalDuration += duration
			}
			modifiedLines = append(modifiedLines, line)
		} else if strings.HasPrefix(trimmedLine, "segment_") {
			// 从文件名中提取序号
			tsNumber := strings.TrimPrefix(trimmedLine, "segment_")
			tsNumber = strings.TrimSuffix(tsNumber, ".ts")

			// 构造新的 ts 文件路径，使用新的路径格式：records/device{deviceID}
			devicePath := fmt.Sprintf("%s/%s", storage.BucketRecords, h.service.GetDevicePathPrefix(deviceID))
			tsPath := fmt.Sprintf("/api/records/videos/%s/segment_%s.ts?bucket=%s", folderName, tsNumber, devicePath)
			modifiedLines = append(modifiedLines, tsPath)
		} else {
			modifiedLines = append(modifiedLines, line)
		}
	}

	modifiedContent := strings.Join(modifiedLines, "\n")

	// 设置响应头
	c.Header("Content-Type", "application/vnd.apple.mpegurl")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Cache-Control", "max-age=3600")

	c.String(http.StatusOK, modifiedContent)
}

// GetSegment 处理获取视频片段的请求
func (h *Handler) GetSegment(c *gin.Context) {
	var req struct {
		Bucket string `form:"bucket" binding:"required"`
	}

	if err := c.ShouldBindQuery(&req); err != nil {
		log.Printf("[GetSegment] 参数错误: %v, 请求参数: %+v", err, c.Request.URL.Query())
		c.Status(http.StatusNotFound)
		return
	}

	// 从路径中获取完整的文件夹名称（包含 _hls）
	folder := c.Param("folder")
	filename := c.Param("filename")

	// 解析bucket参数以确定使用哪种获取方式
	deviceID, err := h.service.ParseDeviceIDFromPath(req.Bucket)
	var object *minio.Object

	if err == nil {
		// 新格式：records/device{deviceID} - 使用新的GetVideoSegmentFromRecords方法
		object, err = h.service.GetVideoSegmentFromRecords(c.Request.Context(), deviceID, folder, filename)
		if err != nil {
			log.Printf("[GetSegment] 获取视频片段错误(新格式): %v, deviceID=%s, folder=%s, filename=%s", err, deviceID, folder, filename)
			c.Status(http.StatusNotFound)
			return
		}
	} else {
		// 旧格式：直接桶名 - 保持向后兼容
		object, err = h.service.GetVideoSegment(c.Request.Context(), req.Bucket, folder, filename)
		if err != nil {
			log.Printf("[GetSegment] 获取视频片段错误(旧格式): %v, bucket=%s, folder=%s, filename=%s", err, req.Bucket, folder, filename)
			c.Status(http.StatusNotFound)
			return
		}
	}
	defer object.Close()

	stat, err := object.Stat()
	if err != nil {
		log.Printf("[GetSegment] 获取文件信息错误: %v, bucket=%s, folder=%s, filename=%s", err, req.Bucket, folder, filename)
		c.Status(http.StatusNotFound)
		return
	}

	c.Header("Content-Type", "video/MP2T")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Cache-Control", "max-age=3600")

	c.DataFromReader(http.StatusOK, stat.Size, "video/MP2T", object, nil)
}

// GetThumbnail 处理直接获取缩略图的请求
func (h *Handler) GetThumbnail(c *gin.Context) {
	// 从URL路径中获取编码的文件夹名称
	encodedFolder := c.Param("folder")

	// URL解码
	folderName, err := url.QueryUnescape(encodedFolder)
	if err != nil {
		log.Printf("[GetThumbnail] URL解码失败: %v, encodedFolder=%s", err, encodedFolder)
		c.Status(http.StatusBadRequest)
		return
	}

	// 获取bucket参数
	bucket := c.Query("bucket")
	if bucket == "" {
		log.Printf("[GetThumbnail] 缺少bucket参数")
		c.Status(http.StatusBadRequest)
		return
	}

	// 解析时间格式，支持多种格式
	var timestamp time.Time
	timeFormats := []string{
		"2006-01-02T15:04:05+08:00", // 2025-01-23T20:46:05+08:00
		"2006-01-02T15:04:05Z07:00", // 2025-01-23T20:46:05+08:00
		"2006-01-02T15:04:05 08:00", // 2025-06-17T19:21:26 08:00
		"2006-01-02T15:04:05-07:00", // 其他时区格式
		"2006-01-02T15:04:05Z",      // UTC格式
	}

	parsed := false
	for _, format := range timeFormats {
		if t, err := time.Parse(format, folderName); err == nil {
			timestamp = t
			parsed = true
			break
		}
	}

	if !parsed {
		log.Printf("[GetThumbnail] 时间格式解析失败: %s", folderName)
		c.Status(http.StatusBadRequest)
		return
	}

	// 从bucket中提取设备ID
	deviceID, err := h.service.ParseDeviceIDFromPath(bucket)
	if err != nil {
		log.Printf("[GetThumbnail] 路径解析错误: %v, bucket=%s", err, bucket)
		c.Status(http.StatusBadRequest)
		return
	}

	// 使用storage service获取缩略图
	object, err := h.service.GetThumbnail(c.Request.Context(), deviceID, timestamp)
	if err != nil {
		log.Printf("[GetThumbnail] 获取缩略图失败: %v, deviceID=%s, timestamp=%s", err, deviceID, timestamp.Format(time.RFC3339))
		c.Status(http.StatusNotFound)
		return
	}
	defer object.Close()

	stat, err := object.Stat()
	if err != nil {
		log.Printf("[GetThumbnail] 获取缩略图信息失败: %v, deviceID=%s", err, deviceID)
		c.Status(http.StatusNotFound)
		return
	}

	// 设置响应头
	c.Header("Content-Type", "image/jpeg")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Cache-Control", "max-age=86400") // 缓存1天

	// 返回图片数据
	c.DataFromReader(http.StatusOK, stat.Size, "image/jpeg", object, nil)
}
