package video

import "time"

// VideoRequest 请求参数结构
type VideoRequest struct {
	BucketName string `form:"bucket" binding:"required"` // 添加 bucket 参数
}

// VideoInfo 视频信息结构
type VideoInfo struct {
	ID          string `json:"id"`           // 视频ID（文件夹名）
	Title       string `json:"title"`        // 视频标题
	CreateTime  string `json:"create_time"`  // 创建时间
	PlaylistURL string `json:"playlist_url"` // m3u8播放地址
}

// VideoListRequest 视频列表请求参数
type VideoListRequest struct {
	Path  string `form:"path" binding:"required"`
	Start string `form:"start"` // 开始时间 格式: 2006-01-02
	End   string `form:"end"`   // 结束时间 格式: 2006-01-02
}

// VideoItem 单个视频信息
type VideoItem struct {
	Name   string    `json:"name"`   // 视频名称
	Time   time.Time `json:"time"`   // 视频时间
	Source string    `json:"source"` // 视频源
}

// VideoListResponse 视频列表响应
type VideoListResponse struct {
	Items []VideoItem `json:"items"`           // 视频列表
	Error string      `json:"error,omitempty"` // 错误信息
}

// APIResponse API通用响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// RecordSegment 视频片段信息
type RecordSegment struct {
	Start        int64   `json:"start"`
	Duration     string  `json:"duration"`
	URL          string  `json:"url"`
	WeightLitter float64 `json:"weight_litter"`
	WeightCat    float64 `json:"weight_cat"`
	WeightWaste  float64 `json:"weight_waste"`
	ThumbnailURL string  `json:"thumbnail_url"`
	AnimalID        string  `json:"animal_id"`
}
