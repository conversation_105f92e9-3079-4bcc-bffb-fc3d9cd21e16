package types

// LogtoUserInfo 包含 Logto 用户信息
type LogtoUserInfo struct {
    Sub           string    `json:"sub"`        // Logto 用户 ID
    Name          string    `json:"name"`       // Google 显示名称
    Email         string    `json:"email"`      // Google 邮箱
    Picture       string    `json:"picture"`    // Google 头像
    EmailVerified bool      `json:"email_verified"`
    Locale        string    `json:"locale"`
    UpdatedAt     int64     `json:"updated_at"` // Unix timestamp in milliseconds
} 