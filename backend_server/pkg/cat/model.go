package cat

import "time"

// Cat 猫咪基本信息
type Cat struct {
	CatID     string    `json:"cat_id" gorm:"primaryKey;column:cat_id"`
	UserID    string    `json:"user_id" gorm:"column:user_id;not null;index"`
	Name      string    `json:"name" gorm:"not null"`
	Birthday  time.Time `json:"birthday" gorm:"type:date"`
	Gender    int8      `json:"gender" gorm:"default:0"`
	Breed     string    `json:"breed" gorm:"type:varchar(32)"`
	Color     string    `json:"color" gorm:"type:varchar(32)"`
	Weight    float64   `json:"weight"`
	AvatarURL string    `json:"avatar_url" gorm:"type:varchar(255)"`
	Status    int8      `json:"status" gorm:"default:1;index"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// CatBehavior 猫咪行为特征
type CatBehavior struct {
	CatID       string    `json:"cat_id" gorm:"primaryKey;column:cat_id"`
	Activity    string    `json:"activity"`
	Personality string    `json:"personality"`
	Habits      string    `json:"habits"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// CatMetricsDaily 猫咪每日统计
type CatMetricsDaily struct {
	CatID       string    `json:"cat_id" gorm:"primaryKey;column:cat_id"`
	MetricDate  time.Time `json:"metric_date" gorm:"index"`
	ToiletCount int       `json:"toilet_count"`
	AvgDuration float64   `json:"avg_duration"`
	Weight      float64   `json:"weight"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// CatMetricsMonthly 猫咪月度统计
type CatMetricsMonthly struct {
	CatID            string    `json:"cat_id" gorm:"primaryKey;column:cat_id"`
	Year             int       `json:"year" gorm:"index"`
	Month            int       `json:"month" gorm:"index"`
	TotalToiletCount int       `json:"total_toilet_count"`
	AvgToiletCount   float64   `json:"avg_toilet_count"`
	AvgWeight        float64   `json:"avg_weight"`
	CreatedAt        time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt        time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// CatAlert 猫咪健康警报
type CatAlert struct {
	ID          int64     `json:"id" gorm:"primaryKey"`
	CatID       string    `json:"cat_id" gorm:"index"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Level       int8      `json:"level"`
	Status      int8      `json:"status"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// CatState 猫咪状态（医疗、穿衣等特殊状态）
type CatState struct {
	ID               int64      `json:"id" gorm:"primaryKey"`
	UserID           string     `json:"user_id" gorm:"type:varchar(20);index;not null"`
	CatID            string     `json:"cat_id" gorm:"type:varchar(32);index;not null"`
	State            string     `json:"state" gorm:"type:varchar(50);not null"` // "medical", "clothing", "grooming", "other"
	Description      string     `json:"description" gorm:"type:text"`
	StartTime        time.Time  `json:"start_time" gorm:"not null"`
	EndTime          *time.Time `json:"end_time,omitempty"`
	ExpectedEndTime  time.Time  `json:"expected_end_time" gorm:"not null"`
	ExpectedDuration int        `json:"expected_duration_days" gorm:"not null"`
	IsActive         bool       `json:"is_active" gorm:"default:true;index"`
	CreatedAt        time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt        time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName methods to ensure correct table mapping
func (Cat) TableName() string               { return "cats" }
func (CatBehavior) TableName() string       { return "cat_behaviors" }
func (CatMetricsDaily) TableName() string   { return "cat_metrics_daily" }
func (CatMetricsMonthly) TableName() string { return "cat_metrics_monthly" }
func (CatAlert) TableName() string          { return "cat_alerts" }
func (CatState) TableName() string          { return "cat_states" }
