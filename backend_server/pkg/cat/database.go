package cat

import (
	"cabycare-server/config"
	"time"

	"gorm.io/gorm"
)

type Database struct {
	db *gorm.DB
}

func NewDatabase(cfg *config.Config) (*Database, error) {
	db, err := cfg.GetDB()
	if err != nil {
		return nil, err
	}
	return &Database{db: db}, nil
}

// Cat 相关操作
func (d *Database) CreateCat(cat *Cat) error {
	return d.db.Create(cat).Error
}

func (d *Database) GetCat(catID string) (*Cat, error) {
	var cat Cat
	err := d.db.Where("cat_id = ?", catID).First(&cat).Error
	if err != nil {
		return nil, err
	}
	return &cat, nil
}

func (d *Database) UpdateCat(cat *Cat) error {
	return d.db.Save(cat).Error
}

func (d *Database) ListUserCats(userID string) ([]Cat, error) {
	var cats []Cat
	err := d.db.Where("user_id = ? AND status = 1", userID).Find(&cats).Error
	if err != nil {
		return nil, err
	}
	return cats, nil
}

// CatBehavior 相关操作
func (d *Database) CreateCatBehavior(behavior *CatBehavior) error {
	return d.db.Create(behavior).Error
}

func (d *Database) GetCatBehavior(catID string) (*CatBehavior, error) {
	var behavior CatBehavior
	err := d.db.Where("cat_id = ?", catID).First(&behavior).Error
	if err != nil {
		return nil, err
	}
	return &behavior, nil
}

func (d *Database) UpdateCatBehavior(behavior *CatBehavior) error {
	return d.db.Save(behavior).Error
}

// CatMetricsDaily 相关操作
func (d *Database) CreateCatMetricsDaily(metrics *CatMetricsDaily) error {
	return d.db.Create(metrics).Error
}

func (d *Database) GetCatDailyMetrics(catID string, date time.Time) (*CatMetricsDaily, error) {
	var metrics CatMetricsDaily
	err := d.db.Where("cat_id = ? AND metric_date = ?", catID, date).First(&metrics).Error
	if err != nil {
		return nil, err
	}
	return &metrics, nil
}

func (d *Database) UpdateCatMetricsDaily(metrics *CatMetricsDaily) error {
	return d.db.Save(metrics).Error
}

// CatMetricsMonthly 相关操作
func (d *Database) CreateCatMetricsMonthly(metrics *CatMetricsMonthly) error {
	return d.db.Create(metrics).Error
}

func (d *Database) GetCatMonthlyMetrics(catID string, year, month int) (*CatMetricsMonthly, error) {
	var metrics CatMetricsMonthly
	err := d.db.Where("cat_id = ? AND year = ? AND month = ?", catID, year, month).First(&metrics).Error
	if err != nil {
		return nil, err
	}
	return &metrics, nil
}

func (d *Database) UpdateCatMetricsMonthly(metrics *CatMetricsMonthly) error {
	return d.db.Save(metrics).Error
}

// CatAlert 相关操作
func (d *Database) CreateCatAlert(alert *CatAlert) error {
	return d.db.Create(alert).Error
}

func (d *Database) ListCatAlerts(catID string, status int8) ([]CatAlert, error) {
	var alerts []CatAlert
	query := d.db.Where("cat_id = ?", catID)
	if status >= 0 {
		query = query.Where("status = ?", status)
	}
	err := query.Order("created_at DESC").Find(&alerts).Error
	if err != nil {
		return nil, err
	}
	return alerts, nil
}

func (d *Database) UpdateCatAlert(alert *CatAlert) error {
	return d.db.Save(alert).Error
}

// DeleteCat 软删除猫咪
func (d *Database) DeleteCat(catID string, userID string) error {
	return d.db.Model(&Cat{}).
		Where("cat_id = ? AND user_id = ?", catID, userID).
		Update("status", -1).Error
}

// HideCat 隐藏猫咪
func (d *Database) HideCat(catID string, userID string) error {
	return d.db.Model(&Cat{}).
		Where("cat_id = ? AND user_id = ?", catID, userID).
		Update("status", 0).Error
}

// RestoreCat 恢复猫咪（取消隐藏）
func (d *Database) RestoreCat(catID string, userID string) error {
	return d.db.Model(&Cat{}).
		Where("cat_id = ? AND user_id = ?", catID, userID).
		Update("status", 1).Error
}

// ListUserHiddenCats 获取用户隐藏的猫咪列表
func (d *Database) ListUserHiddenCats(userID string) ([]Cat, error) {
	var cats []Cat
	err := d.db.Where("user_id = ? AND status = 0", userID).Find(&cats).Error
	if err != nil {
		return nil, err
	}
	return cats, nil
}

// ListUserAllCats 获取用户所有猫咪（包括隐藏的，不包括已删除的）
func (d *Database) ListUserAllCats(userID string) ([]Cat, error) {
	var cats []Cat
	err := d.db.Where("user_id = ? AND status >= 0", userID).Find(&cats).Error
	if err != nil {
		return nil, err
	}
	return cats, nil
}

// ===== CatState 相关操作 =====

// CreateCatState 创建猫咪状态
func (d *Database) CreateCatState(state *CatState) error {
	return d.db.Create(state).Error
}

// GetActiveCatState 获取活跃的猫咪状态
func (d *Database) GetActiveCatState(userID, catID string) (*CatState, error) {
	var state CatState
	err := d.db.Where("user_id = ? AND cat_id = ? AND is_active = ?", userID, catID, true).
		Order("created_at DESC").
		First(&state).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 没有活跃状态
		}
		return nil, err
	}
	return &state, nil
}

// EndActiveCatState 结束活跃状态
func (d *Database) EndActiveCatState(userID, catID string) error {
	now := time.Now()
	return d.db.Model(&CatState{}).
		Where("user_id = ? AND cat_id = ? AND is_active = ?", userID, catID, true).
		Updates(map[string]interface{}{
			"is_active":  false,
			"end_time":   &now,
			"updated_at": now,
		}).Error
}

// GetExpiredCatStates 获取过期的状态
func (d *Database) GetExpiredCatStates() ([]CatState, error) {
	var states []CatState
	err := d.db.Where("is_active = ? AND expected_end_time < ?", true, time.Now()).
		Find(&states).Error
	if err != nil {
		return nil, err
	}
	return states, nil
}
