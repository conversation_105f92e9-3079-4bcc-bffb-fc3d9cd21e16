package cat

import (
	"cabycare-server/config"
	"crypto/sha256"
	"fmt"
	"log"
	"time"
)

type CatService struct {
	db *Database
}

func NewCatService(cfg *config.Config) (*CatService, error) {
	db, err := NewDatabase(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create cat database: %v", err)
	}

	return &CatService{
		db: db,
	}, nil
}

// CreateCat 创建新猫咪
func (s *CatService) CreateCat(cat *Cat) error {
	return s.db.CreateCat(cat)
}

// GetCat 获取猫咪信息
func (s *CatService) GetCat(catID string) (*Cat, error) {
	return s.db.GetCat(catID)
}

// UpdateCat 更新猫咪信息
func (s *CatService) UpdateCat(cat *Cat) error {
	return s.db.UpdateCat(cat)
}

// ListUserCats 获取用户的所有猫咪
func (s *CatService) ListUserCats(userID string) ([]Cat, error) {
	return s.db.ListUserCats(userID)
}

// CreateCatBehavior 创建猫咪行为特征
func (s *CatService) CreateCatBehavior(behavior *CatBehavior) error {
	return s.db.CreateCatBehavior(behavior)
}

// GetCatBehavior 获取猫咪行为特征
func (s *CatService) GetCatBehavior(catID string) (*CatBehavior, error) {
	return s.db.GetCatBehavior(catID)
}

// UpdateCatBehavior 更新猫咪行为特征
func (s *CatService) UpdateCatBehavior(behavior *CatBehavior) error {
	return s.db.UpdateCatBehavior(behavior)
}

// CreateCatMetricsDaily 创建猫咪每日统计
func (s *CatService) CreateCatMetricsDaily(metrics *CatMetricsDaily) error {
	return s.db.CreateCatMetricsDaily(metrics)
}

// GetCatDailyMetrics 获取猫咪每日统计
func (s *CatService) GetCatDailyMetrics(catID string, date time.Time) (*CatMetricsDaily, error) {
	return s.db.GetCatDailyMetrics(catID, date)
}

// UpdateCatMetricsDaily 更新猫咪每日统计
func (s *CatService) UpdateCatMetricsDaily(metrics *CatMetricsDaily) error {
	return s.db.UpdateCatMetricsDaily(metrics)
}

// CreateCatMetricsMonthly 创建猫咪月度统计
func (s *CatService) CreateCatMetricsMonthly(metrics *CatMetricsMonthly) error {
	return s.db.CreateCatMetricsMonthly(metrics)
}

// GetCatMonthlyMetrics 获取猫咪月度统计
func (s *CatService) GetCatMonthlyMetrics(catID string, year, month int) (*CatMetricsMonthly, error) {
	return s.db.GetCatMonthlyMetrics(catID, year, month)
}

// UpdateCatMetricsMonthly 更新猫咪月度统计
func (s *CatService) UpdateCatMetricsMonthly(metrics *CatMetricsMonthly) error {
	return s.db.UpdateCatMetricsMonthly(metrics)
}

// CreateCatAlert 创建猫咪健康警报
func (s *CatService) CreateCatAlert(alert *CatAlert) error {
	return s.db.CreateCatAlert(alert)
}

// ListCatAlerts 获取猫咪健康警报
func (s *CatService) ListCatAlerts(catID string, status int8) ([]CatAlert, error) {
	return s.db.ListCatAlerts(catID, status)
}

// UpdateCatAlert 更新猫咪健康警报
func (s *CatService) UpdateCatAlert(alert *CatAlert) error {
	return s.db.UpdateCatAlert(alert)
}

// CreateDefaultUnknownCat 为用户创建默认的 unknown 猫咪
func (s *CatService) CreateDefaultUnknownCat(userID string) error {
	// 生成一个基于用户ID的固定cat_id
	hasher := sha256.New()
	hasher.Write([]byte(userID + "_unknown"))
	catID := fmt.Sprintf("c_%x", hasher.Sum(nil)[:8])

	// 创建默认猫咪
	cat := &Cat{
		CatID:     catID,
		UserID:    userID,
		Name:      "Unknown",
		Status:    1,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.db.CreateCat(cat); err != nil {
		return fmt.Errorf("failed to create unknown cat: %v", err)
	}

	log.Printf("Created unknown cat with ID: %s for user: %s", catID, userID)
	return nil
}

// CreateUnknownCat 创建一个未知猫咪及其档案
func (s *CatService) CreateUnknownCat(catID, userID string) error {
	// 创建未知猫咪
	cat := &Cat{
		CatID:     catID,
		UserID:    userID,
		Name:      "You Know Who",
		Birthday:  time.Now(),
		Gender:    0, // 未知性别
		Breed:     "Unknown",
		Color:     "Unknown",
		Status:    1, // 正常状态
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	if err := s.db.CreateCat(cat); err != nil {
		return fmt.Errorf("failed to create unknown cat: %v", err)
	}

	return nil
}

// DeleteCat 软删除猫咪
func (s *CatService) DeleteCat(catID string, userID string) error {
	return s.db.DeleteCat(catID, userID)
}

// HideCat 隐藏猫咪
func (s *CatService) HideCat(catID string, userID string) error {
	return s.db.HideCat(catID, userID)
}

// RestoreCat 恢复猫咪（取消隐藏）
func (s *CatService) RestoreCat(catID string, userID string) error {
	return s.db.RestoreCat(catID, userID)
}

// ListUserHiddenCats 获取用户隐藏的猫咪列表
func (s *CatService) ListUserHiddenCats(userID string) ([]Cat, error) {
	return s.db.ListUserHiddenCats(userID)
}

// ListUserAllCats 获取用户所有猫咪（包括隐藏的，不包括已删除的）
func (s *CatService) ListUserAllCats(userID string) ([]Cat, error) {
	return s.db.ListUserAllCats(userID)
}

// ===== 猫咪状态管理方法 =====

// MarkCatState 标记猫咪状态
func (s *CatService) MarkCatState(userID, catID, state, description string, expectedDuration int) error {
	// 1. 先结束任何现有的活跃状态
	err := s.endActiveState(userID, catID)
	if err != nil {
		log.Printf("Warning: failed to end existing active state: %v", err)
	}

	// 2. 创建新的状态记录
	startTime := time.Now()
	expectedEndTime := startTime.Add(time.Duration(expectedDuration) * 24 * time.Hour)

	catState := &CatState{
		UserID:           userID,
		CatID:            catID,
		State:            state,
		Description:      description,
		StartTime:        startTime,
		ExpectedEndTime:  expectedEndTime,
		ExpectedDuration: expectedDuration,
		IsActive:         true,
	}

	err = s.db.CreateCatState(catState)
	if err != nil {
		return fmt.Errorf("failed to create cat state: %w", err)
	}

	log.Printf("Marked cat %s (user %s) as %s for %d days", catID, userID, state, expectedDuration)
	return nil
}

// EndCatState 结束猫咪状态
func (s *CatService) EndCatState(userID, catID string) error {
	return s.endActiveState(userID, catID)
}

// GetActiveCatState 获取活跃的猫咪状态
func (s *CatService) GetActiveCatState(userID, catID string) (*CatState, error) {
	return s.db.GetActiveCatState(userID, catID)
}

// endActiveState 结束活跃状态
func (s *CatService) endActiveState(userID, catID string) error {
	err := s.db.EndActiveCatState(userID, catID)
	if err != nil {
		return fmt.Errorf("failed to end active state: %w", err)
	}

	log.Printf("Ended active state for cat %s (user %s)", catID, userID)
	return nil
}

// GetExpiredStates 获取过期的状态（用于自动清理）
func (s *CatService) GetExpiredStates() ([]CatState, error) {
	return s.db.GetExpiredCatStates()
}

// AutoCleanupExpiredStates 自动清理过期状态
func (s *CatService) AutoCleanupExpiredStates() error {
	expiredStates, err := s.GetExpiredStates()
	if err != nil {
		return fmt.Errorf("failed to get expired states: %w", err)
	}

	for _, state := range expiredStates {
		err := s.EndCatState(state.UserID, state.CatID)
		if err != nil {
			log.Printf("Failed to auto-end expired state for cat %s: %v", state.CatID, err)
		} else {
			log.Printf("Auto-ended expired state for cat %s (user %s)", state.CatID, state.UserID)
		}
	}

	return nil
}
