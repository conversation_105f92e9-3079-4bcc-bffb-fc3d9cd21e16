package shadow

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"time"
)

// Client 影子模式客户端
type Client struct {
	baseURL    string
	authToken  string
	httpClient *http.Client
}

// NewClient 创建新的影子模式客户端
func NewClient(baseURL, authToken string) *Client {
	return &Client{
		baseURL:   baseURL,
		authToken: authToken,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// SimilarityRequest 相似度查询请求
type SimilarityRequest struct {
	UserID      string `json:"user_id"`
	ImageBase64 string `json:"image_base64"`
	Limit       int    `json:"limit"`
}

// SimilarityResult 相似度查询结果
type SimilarityResult struct {
	Similarity float64                `json:"similarity"`
	Payload    map[string]interface{} `json:"payload"`
}

// SimilarityResponse 相似度查询响应
type SimilarityResponse struct {
	Success bool               `json:"success"`
	Results []SimilarityResult `json:"results"`
	Error   string             `json:"error,omitempty"`
}

// InitCatRequest 初始化猫咪请求
type InitCatRequest struct {
	UserID string    `json:"user_id"`
	Cats   []CatInfo `json:"cats"`
}

// CatInfo 猫咪信息
type CatInfo struct {
	CatID       string `json:"cat_id"`
	Name        string `json:"name"`
	ImageBase64 string `json:"image_base64"`
}

// InitCatResult 初始化猫咪结果
type InitCatResult struct {
	CatID   string `json:"cat_id"`
	Name    string `json:"name"`
	Success bool   `json:"success"`
	Error   string `json:"error,omitempty"`
}

// InitCatResponse 初始化猫咪响应
type InitCatResponse struct {
	Success bool            `json:"success"`
	Results []InitCatResult `json:"results"`
	Error   string          `json:"error,omitempty"`
}

// TestSimilarity 测试相似度查询
func (c *Client) TestSimilarity(ctx context.Context, req *SimilarityRequest) (*SimilarityResponse, error) {
	url := fmt.Sprintf("%s/api/v1/admin/test-similarity", c.baseURL)

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.authToken))

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	var result SimilarityResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &result, nil
}

// InitCats 初始化猫咪特征
func (c *Client) InitCats(ctx context.Context, req *InitCatRequest) (*InitCatResponse, error) {
	url := fmt.Sprintf("%s/api/v1/admin/init-cats", c.baseURL)

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.authToken))

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	var result InitCatResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &result, nil
}

// HealthCheck 健康检查
func (c *Client) HealthCheck(ctx context.Context) error {
	url := fmt.Sprintf("%s/health", c.baseURL)

	httpReq, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("health check failed with status %d: %s", resp.StatusCode, string(body))
	}

	return nil
}

// CatDetectionRequest 猫咪检测请求
type CatDetectionRequest struct {
	ImageBase64      string `json:"image_base64"`
	Task             string `json:"task"`
	ReturnFeatures   bool   `json:"return_features"`
	ReturnConfidence bool   `json:"return_confidence"`
}

// CatDetectionResponse 猫咪检测响应
type CatDetectionResponse struct {
	Success            bool               `json:"success"`
	PredictedCat       string             `json:"predicted_cat,omitempty"`
	Confidence         float64            `json:"confidence,omitempty"`
	ClassProbabilities map[string]float64 `json:"class_probabilities,omitempty"`
	ProcessTime        float64            `json:"process_time,omitempty"`
	Task               string             `json:"task,omitempty"`
	Message            string             `json:"message"`
	Error              string             `json:"error,omitempty"`
	RequestID          string             `json:"request_id,omitempty"`
}

// DetectCat 调用猫咪检测接口
func (c *Client) DetectCat(ctx context.Context, imageBase64 string) (*CatDetectionResponse, error) {
	url := fmt.Sprintf("%s/api/v1/vision/detect/cat", c.baseURL)

	// 解码base64图片数据
	imageData, err := base64.StdEncoding.DecodeString(imageBase64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64 image: %w", err)
	}

	// 创建multipart form data
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加图片文件
	part, err := writer.CreateFormFile("image", "image.jpg")
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err := part.Write(imageData); err != nil {
		return nil, fmt.Errorf("failed to write image data: %w", err)
	}

	// 添加其他参数
	writer.WriteField("task", "predict")
	writer.WriteField("return_features", "false")
	writer.WriteField("return_confidence", "true")

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close multipart writer: %w", err)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", writer.FormDataContentType())
	httpReq.Header.Set("Authorization", "Bearer "+c.authToken)

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	var result CatDetectionResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &result, nil
}
