package notification

import (
	"encoding/json"
	"time"
)

// NotificationType 通知类型
type NotificationType string

const (
	TypeDaily    NotificationType = "daily"    // 日常类
	TypeAbnormal NotificationType = "abnormal" // 异常类
	TypeStats    NotificationType = "stats"    // 统计类
	TypeHealth   NotificationType = "health"   // 健康类
	TypeMaintain NotificationType = "maintain" // 维护类
)

// NotificationSubType 通知子类型
type NotificationSubType string

const (
    // 日常类子类型
    SubTypeToilet     NotificationSubType = "toilet"      // 猫咪如厕
    SubTypeCleanup    NotificationSubType = "cleanup"     // 需要清理
    SubTypeLitterLow  NotificationSubType = "litterLow"   // 猫砂不足

    // 异常类子类型
    SubTypeDogIntrude NotificationSubType = "dogIntrude"  // 狗入侵
    SubTypeForeignObj NotificationSubType = "foreignObj"  // 异物入侵
    SubTypeDeviceErr  NotificationSubType = "deviceErr"   // 设备异常

    // 统计类子类型
    SubTypeDailyStats NotificationSubType = "dailyStats"  // 每日统计
    SubTypeWeekStats  NotificationSubType = "weekStats"   // 每周统计
    SubTypeMonthStats NotificationSubType = "monthStats"  // 每月统计

    // 健康类子类型
    SubTypeWeightChange NotificationSubType = "weightChange" // 体重变化
    SubTypeBehaviorChange NotificationSubType = "behaviorChange" // 行为变化
    SubTypeAppearanceChange NotificationSubType = "appearanceChange" // 外观变化

    // 维护类子类型
    SubTypeNewCat     NotificationSubType = "newCat"      // 新猫咪
    SubTypeCatMissing NotificationSubType = "catMissing"  // 猫咪失踪
    SubTypeCatReturn  NotificationSubType = "catReturn"   // 猫咪返回

    // 新增的健康类子类型
    SubTypeHealth NotificationSubType = "health"
)

// NotificationPriority 通知优先级
type NotificationPriority int

const (
    PriorityLow    NotificationPriority = 1
    PriorityNormal NotificationPriority = 2
    PriorityHigh   NotificationPriority = 3
)

// NotificationStatus 通知状态
type NotificationStatus int

const (
	NotificationStatusPending   NotificationStatus = 0
	NotificationStatusDelivered NotificationStatus = 1
	NotificationStatusRead      NotificationStatus = 2
)

// PendingNotification 待处理的通知
type PendingNotification struct {
	VideoID   string
	Type      NotificationType
	SubType   NotificationSubType
	UserID    string
	Title     string
	Body      string
	Priority  int
	Metadata  json.RawMessage
	CreatedAt time.Time
}

// Notification 通知信息
type Notification struct {
	ID          int64              `json:"id" gorm:"primaryKey;column:id;autoIncrement"`
	NoticeID    string             `json:"notice_id" gorm:"column:notice_id"`
	UserID      string             `json:"user_id" gorm:"column:user_id"`
	Type        NotificationType   `json:"type" gorm:"column:type"`
	SubType     NotificationSubType `json:"sub_type" gorm:"column:subtype"`
	Title       string             `json:"title" gorm:"column:title"`
	Body        string             `json:"body" gorm:"column:body"`
	Priority    int                `json:"priority" gorm:"column:priority"`
	Status      NotificationStatus `json:"status" gorm:"column:status"`
	Metadata    json.RawMessage   `json:"metadata" gorm:"column:metadata"`
	DeliveredAt *time.Time         `json:"delivered_at" gorm:"column:delivered_at"`
	ReadAt      *time.Time         `json:"read_at" gorm:"column:read_at"`
	CreatedAt   time.Time          `json:"created_at" gorm:"column:created_at"`
	UpdatedAt   time.Time          `json:"updated_at" gorm:"column:updated_at"`
}

// QuietNotification 免打扰期间的通知
type QuietNotification struct {
    ID          int64     `json:"id" gorm:"primaryKey;autoIncrement"`
    UserID      string    `json:"user_id" gorm:"column:user_id"`
    CatID       string    `json:"cat_id" gorm:"column:cat_id"`
    Type        string    `json:"type" gorm:"column:type"`
    SubType     string    `json:"subtype" gorm:"column:subtype"`
    Title       string    `json:"title" gorm:"column:title"`
    Body        string    `json:"body" gorm:"column:body"`
    Duration    int       `json:"duration" gorm:"column:duration"` // 使用秒数
    Count       int       `json:"count" gorm:"column:count"`       // 通知次数
    CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
    UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// NotificationMetadata 通知元数据基础接口
type NotificationMetadata interface {
    Validate() error
}

// DailyMetadata 日常类通知元数据
type DailyMetadata struct {
    DeviceID string `json:"device_id"` // 设备ID
    CatID    string `json:"cat_id"`    // 猫咪ID
    Action   string `json:"action"`    // 具体动作
    Value    string `json:"value"`     // 相关数值
}

// AbnormalMetadata 异常类通知元数据
type AbnormalMetadata struct {
    DeviceID    string `json:"device_id"`    // 设备ID
    ErrorCode   string `json:"error_code"`   // 错误代码
    Severity    int    `json:"severity"`     // 严重程度
    Description string `json:"description"`  // 详细描述
}

// StatsMetadata 统计类通知元数据
type StatsMetadata struct {
    StartDate int64             `json:"start_date"` // 统计开始时间
    EndDate   int64             `json:"end_date"`   // 统计结束时间
    Metrics   map[string]string `json:"metrics"`    // 统计指标
}

// HealthMetadata 健康类通知元数据
type HealthMetadata struct {
    CatID         string  `json:"cat_id"`         // 猫咪ID
    HealthMetric  string  `json:"health_metric"`  // 健康指标
    OldValue      string  `json:"old_value"`      // 原值
    NewValue      string  `json:"new_value"`      // 新值
    ChangePercent float64 `json:"change_percent"` // 变化百分比
}

// MaintainMetadata 维护类通知元数据
type MaintainMetadata struct {
    MaintenanceType string `json:"maintenance_type"` // 维护类型
    Severity        int    `json:"severity"`         // 严重程度
    CatID           string `json:"cat_id,omitempty"` // 相关猫咪ID
}

// NotificationSettings 用户通知设置
type NotificationSettings struct {
    UserID           string    `json:"user_id"`
    EnableDaily      bool      `json:"enable_daily"`       // 是否启用日常通知
    EnableStats      bool      `json:"enable_stats"`       // 是否启用统计通知
    QuietHoursStart  int       `json:"quiet_hours_start"`  // 免打扰开始时间(分钟数，0-1439)
    QuietHoursEnd    int       `json:"quiet_hours_end"`    // 免打扰结束时间(分钟数，0-1439)
    Timezone         string    `json:"timezone"`           // 时区
    UpdatedAt        time.Time `json:"updated_at"`
}

// PushProvider 推送服务提供商
type PushProvider string

const (
    ProviderAPNS   PushProvider = "apns"
    ProviderFCM    PushProvider = "fcm"
    ProviderHuawei PushProvider = "huawei"
)

// DeviceToken 设备推送令牌
type DeviceToken struct {
	ID          string     `json:"id" gorm:"primaryKey;column:id"`
	UserID      string     `json:"user_id" gorm:"column:user_id"`
	DeviceID    string     `json:"device_id" gorm:"column:device_id"`
	Provider    PushProvider `json:"provider" gorm:"column:provider"`
	Token       string     `json:"token" gorm:"column:token"`
	Environment string     `json:"environment" gorm:"column:environment"` // production/sandbox
	CreatedAt   time.Time  `json:"created_at" gorm:"column:created_at"`
	UpdatedAt   time.Time  `json:"updated_at" gorm:"column:updated_at"`
	LastUsedAt  *time.Time `json:"last_used_at" gorm:"column:last_used_at"`
}