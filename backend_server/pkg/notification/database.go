package notification

import (
	"cabycare-server/config"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Database struct {
	db *gorm.DB
}

// Cat 结构体定义
type Cat struct {
	CatID     string    `gorm:"column:cat_id;primaryKey"`
	UserID    string    `gorm:"column:user_id"`
	Name      string    `gorm:"column:name"`
	Status    int       `gorm:"column:status"`
	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`
}

// TableName 指定表名
func (Cat) TableName() string {
	return "cats"
}

func NewDatabase(cfg *config.Config) (*Database, error) {
	// 构建 MySQL DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.MySQL.User,
		cfg.MySQL.Password,
		cfg.MySQL.Host,
		cfg.MySQL.Port,
		cfg.MySQL.Database,
	)

	// 打开数据库连接
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %v", err)
	}

	return &Database{db: db}, nil
}

// CreateNotification 创建通知记录
func (d *Database) CreateNotification(n *Notification) error {
	return d.db.Create(n).Error
}

// GetNotification 获取单个通知
func (d *Database) GetNotification(id string) (*Notification, error) {
	var n Notification
	if err := d.db.First(&n, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &n, nil
}

// GetUserNotifications 获取用户的通知列表
func (d *Database) GetUserNotifications(userID string, limit, offset int) ([]Notification, error) {
	var notifications []Notification
	err := d.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&notifications).Error
	return notifications, err
}

// UpdateNotificationStatus 更新通知状态
func (d *Database) UpdateNotificationStatus(id string, status NotificationStatus) error {
	updates := map[string]interface{}{
		"status": status,
	}
	if status == NotificationStatusDelivered {
		updates["delivered_at"] = time.Now()
	}
	return d.db.Model(&Notification{}).Where("notice_id = ?", id).Updates(updates).Error
}

// MarkNotificationAsRead 标记通知为已读
func (d *Database) MarkNotificationAsRead(id string) error {
	return d.db.Model(&Notification{}).Where("notice_id = ?", id).Update("read_at", time.Now()).Error
}

// GetUserNotificationSettings 获取用户通知设置
func (d *Database) GetUserNotificationSettings(userID string) (*NotificationSettings, error) {
	var settings NotificationSettings
	err := d.db.First(&settings, "user_id = ?", userID).Error
	if err == gorm.ErrRecordNotFound {
		// create it as return default settings in database, then return it
		settings = NotificationSettings{
			UserID:          userID,
			EnableDaily:     true,
			EnableStats:     true,
			QuietHoursStart: 1320, // 22:00
			QuietHoursEnd:   420,  // 07:00
		}
		err = d.db.Create(&settings).Error
		if err != nil {
			return nil, err
		}
		return &settings, nil
	}
	if err != nil {
		return nil, err
	}
	return &settings, nil
}

// UpdateUserNotificationSettings 更新用户通知设置
func (d *Database) UpdateUserNotificationSettings(settings *NotificationSettings) error {
	settings.UpdatedAt = time.Now()
	// 先尝试查找是否存在
	var exists NotificationSettings
	err := d.db.First(&exists, "user_id = ?", settings.UserID).Error
	if err == gorm.ErrRecordNotFound {
		// 不存在则创建
		return d.db.Create(settings).Error
	}
	// 存在则更新
	return d.db.Model(&NotificationSettings{}).
		Where("user_id = ?", settings.UserID).
		Updates(map[string]interface{}{
			"enable_daily":      settings.EnableDaily,
			"enable_stats":      settings.EnableStats,
			"quiet_hours_start": settings.QuietHoursStart,
			"quiet_hours_end":   settings.QuietHoursEnd,
			"updated_at":        settings.UpdatedAt,
		}).Error
}

// SaveDeviceToken 保存设备推送令牌
func (d *Database) SaveDeviceToken(token *DeviceToken) error {
	// 转换为 clients_tokens 表结构
	clientToken := struct {
		ClientID    string    `gorm:"column:client_id"`
		UserID      string    `gorm:"column:user_id"`
		ClientToken string    `gorm:"column:client_token"`
		TokenType   string    `gorm:"column:token_type"`
		IsSandbox   bool      `gorm:"column:is_sandbox"`
		Status      int8      `gorm:"column:status"`
		CreatedAt   time.Time `gorm:"column:created_at"`
		UpdatedAt   time.Time `gorm:"column:updated_at"`
	}{
		ClientID:    token.DeviceID,
		UserID:      token.UserID,
		ClientToken: token.Token,
		TokenType:   string(token.Provider),
		IsSandbox:   token.Environment == "sandbox",
		Status:      1,
		CreatedAt:   token.CreatedAt,
		UpdatedAt:   token.UpdatedAt,
	}

	// 添加日志
	fmt.Printf("Saving device token - DeviceID: %s, Environment: %s, IsSandbox: %v\n",
		token.DeviceID, token.Environment, clientToken.IsSandbox)

	// 使用 Upsert 操作
	result := d.db.Table("clients_tokens").
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "client_id"}},
			UpdateAll: true,
		}).
		Create(&clientToken)

	if result.Error != nil {
		return result.Error
	}

	// 验证更新后的值
	var updatedToken struct {
		ClientID  string `gorm:"column:client_id"`
		IsSandbox bool   `gorm:"column:is_sandbox"`
	}
	if err := d.db.Table("clients_tokens").
		Select("client_id, is_sandbox").
		Where("client_id = ?", token.DeviceID).
		First(&updatedToken).Error; err != nil {
		return fmt.Errorf("failed to verify updated token: %v", err)
	}

	return nil
}

// GetUserDeviceTokens 获取用户的所有设备令牌
func (d *Database) GetUserDeviceTokens(userID string) ([]DeviceToken, error) {
	var clientTokens []struct {
		ClientID    string    `gorm:"column:client_id"`
		UserID      string    `gorm:"column:user_id"`
		ClientToken string    `gorm:"column:client_token"`
		TokenType   string    `gorm:"column:token_type"`
		IsSandbox   bool      `gorm:"column:is_sandbox"`
		CreatedAt   time.Time `gorm:"column:created_at"`
		UpdatedAt   time.Time `gorm:"column:updated_at"`
	}

	if err := d.db.Table("clients_tokens").
		Where("user_id = ? AND status = 1", userID).
		Find(&clientTokens).Error; err != nil {
		return nil, err
	}

	// 转换为 DeviceToken 结构
	tokens := make([]DeviceToken, len(clientTokens))
	for i, ct := range clientTokens {
		tokens[i] = DeviceToken{
			ID:          ct.ClientID, // 使用 client_id 作为 token id
			UserID:      ct.UserID,
			DeviceID:    ct.ClientID,
			Provider:    PushProvider(ct.TokenType),
			Token:       ct.ClientToken,
			Environment: map[bool]string{true: "sandbox", false: "production"}[ct.IsSandbox],
			CreatedAt:   ct.CreatedAt,
			UpdatedAt:   ct.UpdatedAt,
		}
	}

	return tokens, nil
}

// DeleteDeviceToken 删除设备推送令牌
func (d *Database) DeleteDeviceToken(deviceID string) error {
	return d.db.Table("clients_tokens").
		Where("client_id = ?", deviceID).
		Update("status", 0).Error // 软删除，只更新状态
}

// StorePendingNotification 存储待处理的通知
func (d *Database) StorePendingNotification(pending *PendingNotification) error {
	// 使用 gorm 创建记录
	return d.db.Table("pending_notifications").Create(&struct {
		VideoID   string          `gorm:"column:video_id"`
		Type      string          `gorm:"column:type"`
		SubType   string          `gorm:"column:subtype"`
		UserID    string          `gorm:"column:user_id"`
		Title     string          `gorm:"column:title"`
		Body      string          `gorm:"column:body"`
		Priority  int             `gorm:"column:priority"`
		Metadata  json.RawMessage `gorm:"column:metadata"`
		CreatedAt time.Time       `gorm:"column:created_at"`
	}{
		VideoID:   pending.VideoID,
		Type:      string(pending.Type),
		SubType:   string(pending.SubType),
		UserID:    pending.UserID,
		Title:     pending.Title,
		Body:      pending.Body,
		Priority:  pending.Priority,
		Metadata:  pending.Metadata,
		CreatedAt: pending.CreatedAt,
	}).Error
}

// GetPendingNotification 获取待处理的通知
func (d *Database) GetPendingNotification(videoID string) (*PendingNotification, error) {
	var result struct {
		VideoID   string          `gorm:"column:video_id"`
		Type      string          `gorm:"column:type"`
		SubType   string          `gorm:"column:subtype"`
		UserID    string          `gorm:"column:user_id"`
		Title     string          `gorm:"column:title"`
		Body      string          `gorm:"column:body"`
		Priority  int             `gorm:"column:priority"`
		Metadata  json.RawMessage `gorm:"column:metadata"`
		CreatedAt time.Time       `gorm:"column:created_at"`
	}

	if err := d.db.Table("pending_notifications").
		Where("video_id = ?", videoID).
		First(&result).Error; err != nil {
		return nil, err
	}

	return &PendingNotification{
		VideoID:   result.VideoID,
		Type:      NotificationType(result.Type),
		SubType:   NotificationSubType(result.SubType),
		UserID:    result.UserID,
		Title:     result.Title,
		Body:      result.Body,
		Priority:  result.Priority,
		Metadata:  result.Metadata,
		CreatedAt: result.CreatedAt,
	}, nil
}

// DeletePendingNotification 删除待处理的通知
func (d *Database) DeletePendingNotification(videoID string) error {
	return d.db.Table("pending_notifications").
		Where("video_id = ?", videoID).
		Delete(nil).Error
}

// DeletePendingNotificationsOlderThan deletes pending notifications older than the given time
func (d *Database) DeletePendingNotificationsOlderThan(cutoffTime time.Time) (int64, error) {
	result := d.db.Table("pending_notifications").
		Where("created_at < ?", cutoffTime).
		Delete(nil)

	return result.RowsAffected, result.Error
}

// DeleteNotification 删除通知
func (d *Database) DeleteNotification(id int64) error {
	return d.db.Delete(&Notification{}, id).Error
}

// CreateQuietNotification 创建免打扰通知
func (d *Database) CreateQuietNotification(notification *QuietNotification) error {
	return d.db.Create(notification).Error
}

// GetQuietNotifications 获取所有免打扰通知
func (d *Database) GetQuietNotifications() ([]QuietNotification, error) {
	var notifications []QuietNotification
	if err := d.db.Find(&notifications).Error; err != nil {
		return nil, err
	}
	return notifications, nil
}

// DeleteQuietNotification 删除免打扰通知
func (d *Database) DeleteQuietNotification(id int64) error {
	return d.db.Delete(&QuietNotification{}, id).Error
}

// DeleteQuietNotificationsOlderThan deletes quiet notifications older than the given time
func (d *Database) DeleteQuietNotificationsOlderThan(cutoffTime time.Time) (int64, error) {
	result := d.db.Where("created_at < ?", cutoffTime).
		Delete(&QuietNotification{})

	return result.RowsAffected, result.Error
}

// CheckCatExists 检查猫咪是否存在
func (db *Database) CheckCatExists(catID string) (bool, error) {
	var count int64
	err := db.db.Model(&Cat{}).Where("cat_id = ?", catID).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// CreateCat 创建猫咪记录
func (db *Database) CreateCat(cat *Cat) error {
	return db.db.Create(cat).Error
}
