# 工厂数据存储API文档

## 概述

工厂数据存储系统主要负责管理工厂生产过程中的SN号申请、存储和状态管理。系统支持8字符16进制SN号的生成，确保每个SN号的唯一性。

## 功能特性

1. **SN号生成**: 自动生成8字符唯一16进制SN号
2. **批量申请**: 支持一次申请多个SN号（最多100个）
3. **状态管理**: 支持SN号状态跟踪（已申请、已使用、已废弃）
4. **查询功能**: 支持多条件查询SN号记录
5. **日志记录**: 完整的操作日志记录
6. **统计信息**: 提供SN号使用统计

## 数据库配置

### 环境变量

在`.env`文件中配置以下变量：

```bash
# 工厂数据库配置
FACTORY_DATABASE=factory_db
FACTORY_USER=factory_user
FACTORY_PASSWORD=your_factory_password_here
```

### 配置文件

在`config/config.yaml`中添加工厂数据库配置：

```yaml
factory_db:
  host: "localhost"
  port: 3306
  user: "factory_user"
  password: "your_factory_password_here"
  database: "factory_db"
```

## API接口

### 1. 申请SN号

**接口**: `POST /api/factory/sn/apply`

**请求体**:
```json
{
  "device_type": "toilet_v1",
  "production_line": "line_001",
  "batch_number": "batch_20250715_001",
  "operator": "张三",
  "quantity": 10,
  "remark": "测试批次"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "sn_list": [
      "10000000",
      "10000001",
      "..."
    ],
    "count": 10,
    "request_id": "uuid-string"
  }
}
```

### 2. 查询SN号

**接口**: `GET /api/factory/sn/query`

**查询参数**:
- `sn`: SN号（精确匹配）
- `status`: 状态（1-已申请，2-已使用，3-已废弃）
- `device_type`: 设备类型
- `production_line`: 生产线
- `batch_number`: 批次号
- `operator`: 操作员
- `start_time`: 开始时间（格式：2006-01-02 15:04:05）
- `end_time`: 结束时间
- `page`: 页码（默认1）
- `page_size`: 页大小（默认20，最大100）

**响应**:
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": 1,
        "sn": "10000001",
        "status": 1,
        "device_type": "toilet_v1",
        "production_line": "line_001",
        "batch_number": "batch_20250715_001",
        "operator": "张三",
        "applied_at": "2025-07-15T10:00:00Z",
        "used_at": null,
        "created_at": "2025-07-15T10:00:00Z",
        "updated_at": "2025-07-15T10:00:00Z",
        "remark": "测试批次"
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20
  }
}
```

### 3. 获取SN详细信息

**接口**: `GET /api/factory/sn/{sn}`

**响应**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "sn": "10000001",
    "status": 1,
    "device_type": "toilet_v1",
    "production_line": "line_001",
    "batch_number": "batch_20250715_001",
    "operator": "张三",
    "applied_at": "2025-07-15T10:00:00Z",
    "used_at": null,
    "created_at": "2025-07-15T10:00:00Z",
    "updated_at": "2025-07-15T10:00:00Z",
    "remark": "测试批次"
  }
}
```

### 4. 更新SN状态

**接口**: `PUT /api/factory/sn/update`

**请求体**:
```json
{
  "sn": "10000001",
  "status": 2,
  "operator": "李四",
  "remark": "设备已生产完成"
}
```

**响应**:
```json
{
  "success": true,
  "message": "SN status updated successfully"
}
```

### 5. 检查SN是否存在

**接口**: `GET /api/factory/sn/{sn}/check`

**响应**:
```json
{
  "success": true,
  "data": {
    "sn": "10000001",
    "exists": true
  }
}
```

### 6. 获取统计信息

**接口**: `GET /api/factory/statistics`

**响应**:
```json
{
  "success": true,
  "data": {
    "total_count": 1000,
    "status_stats": [
      {"status": 1, "count": 500},
      {"status": 2, "count": 400},
      {"status": 3, "count": 100}
    ],
    "today_count": 50
  }
}
```

## 状态说明

- **1**: 已申请未使用
- **2**: 已使用
- **3**: 已废弃

## 部署说明

1. 复制`.env.template`为`.env`并填写实际配置
2. 复制`config/config.yaml.template`为`config/config.yaml`并填写实际配置
3. 运行`docker-compose up -d`启动服务

系统会自动创建工厂数据库和相关表结构。

## 注意事项

1. SN号为8字符16进制，从10000000开始递增（对应十进制268435456）
2. 每次申请最多支持100个SN号
3. SN号一旦生成即不可重复使用
4. 所有操作都会记录详细的日志信息
5. 建议定期备份工厂数据库
