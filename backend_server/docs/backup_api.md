# 备份API文档

## 概述

备份API用于获取normal_pop类型的视频记录，支持按时间、设备、猫咪筛选，并提供MinIO直接下载链接。

## 认证

使用工厂Token认证：
```
Authorization: Bearer FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K
```

## API端点

### 1. 获取可备份的视频列表

```http
GET /api/backup/videos
```

**查询参数：**
- `start_time` (可选): 开始时间戳（Unix时间戳，秒）
- `end_time` (可选): 结束时间戳（Unix时间戳，秒）
- `device_id` (可选): 设备ID
- `cat_id` (可选): 猫咪ID
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认20

**示例请求：**
```bash
curl -X GET "https://api.caby.care/api/backup/videos?start_time=1737712707&end_time=1737799107&page=1&limit=10" \
  -H "Authorization: Bearer FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"
```

**响应示例：**
```json
{
  "videos": [
    {
      "video_id": "video_20250124_001",
      "device_id": "device123",
      "cat_id": "cat456",
      "start_time": 1737712707,
      "end_time": 1737712827,
      "weight_litter": 2.5,
      "weight_cat": 4.2,
      "weight_waste": 0.3,
      "behavior_type": "normal_pop",
      "minio_path": "records/device123/2025-01-24_12-45-07_hls",
      "file_list": [],
      "weight_data": {},
      "created_at": "2025-01-24T12:45:07Z"
    }
  ],
  "total": 150,
  "page": 1,
  "limit": 10
}
```

### 2. 获取视频详细信息和下载链接

```http
GET /api/backup/videos/{video_id}
```

**路径参数：**
- `video_id`: 视频ID

**示例请求：**
```bash
curl -X GET "https://api.caby.care/api/backup/videos/video_20250124_001" \
  -H "Authorization: Bearer FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"
```

**响应示例：**
```json
{
  "video_id": "video_20250124_001",
  "device_id": "device123",
  "cat_id": "cat456",
  "start_time": 1737712707,
  "end_time": 1737712827,
  "weight_litter": 2.5,
  "weight_cat": 4.2,
  "weight_waste": 0.3,
  "behavior_type": "normal_pop",
  "minio_path": "records/device123/2025-01-24_12-45-07_hls",
  "file_list": [
    {
      "file_name": "playlist.m3u8",
      "file_type": "playlist",
      "download_url": "https://minio.example.com/records/device123/2025-01-24_12-45-07_hls/playlist.m3u8?X-Amz-Algorithm=...",
      "file_size": 1024
    },
    {
      "file_name": "segment_0001.ts",
      "file_type": "segment",
      "download_url": "https://minio.example.com/records/device123/2025-01-24_12-45-07_hls/segment_0001.ts?X-Amz-Algorithm=...",
      "file_size": 2048000
    },
    {
      "file_name": "cat_weight_1753.json",
      "file_type": "weight",
      "download_url": "https://minio.example.com/records/device123/2025-01-24_12-45-07_hls/cat_weight_1753.json?X-Amz-Algorithm=...",
      "file_size": 512
    }
  ],
  "weight_data": {
    "timestamps": [1737712707, 1737712708, 1737712709],
    "weights": [2.5, 2.7, 2.8],
    "sensor_data": {...}
  },
  "created_at": "2025-01-24T12:45:07Z"
}
```

## 数据结构说明

### BackupVideoInfo
- `video_id`: 视频唯一标识
- `device_id`: 设备ID
- `cat_id`: 猫咪ID（来自分析结果）
- `start_time`: 视频开始时间（Unix时间戳）
- `end_time`: 视频结束时间（Unix时间戳）
- `weight_litter`: 猫砂重量
- `weight_cat`: 猫咪重量
- `weight_waste`: 排泄物重量
- `behavior_type`: 行为类型（固定为"normal_pop"）
- `minio_path`: MinIO中的文件夹路径
- `file_list`: 文件列表（仅在详细信息API中返回）
- `weight_data`: 重量序列数据（仅在详细信息API中返回）
- `created_at`: 记录创建时间

### BackupVideoFile
- `file_name`: 文件名
- `file_type`: 文件类型（playlist/segment/weight/other）
- `download_url`: 预签名下载URL（有效期1小时）
- `file_size`: 文件大小（字节）

## 错误响应

```json
{
  "error": "错误描述"
}
```

常见错误码：
- `400`: 参数错误
- `401`: 认证失败
- `404`: 视频不存在或不是normal_pop类型
- `500`: 服务器内部错误

## 使用说明

1. 所有API都需要使用工厂Token进行认证
2. 预签名下载URL有效期为1小时，过期后需要重新获取
3. 只返回behavior_type为"normal_pop"的视频记录
4. 文件下载支持断点续传
5. 建议使用分页查询，避免一次性获取过多数据
