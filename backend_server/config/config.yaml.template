
# 这是配置文件模板，复制为config.yaml并填写实际值

minio:
  endpoint: "***************:9000"
  access_key: "minioadmin"  # 替换为您的MinIO访问密钥
  secret_key: "minioadmin"  # 替换为您的MinIO密钥
  use_ssl: false

mysql:
  host: "***************"
  port: 3306
  user: "meowth"       # 替换为您的MySQL用户名
  password: "changeme"    # 替换为您的MySQL密码
  database: "cats_db"

factory_db:
  host: "***************"
  port: 3306
  user: "factory_user"    # 工厂数据库用户名
  password: "changeme"    # 替换为您的工厂数据库密码
  database: "factory_db"