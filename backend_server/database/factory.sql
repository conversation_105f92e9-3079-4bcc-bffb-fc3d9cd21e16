-- 工厂数据库创建脚本
-- 用于管理工厂生产过程中的SN号申请和存储

CREATE DATABASE `factory_db` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `factory_db`;

-- SN号记录表
CREATE TABLE `sn_records` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sn` varchar(64) NOT NULL COMMENT '复合SN号：随机16进制_PCB_SN_时间戳',
  `random_hex` varchar(8) NOT NULL COMMENT '8位随机16进制',
  `pcb_sn` varchar(32) NOT NULL COMMENT 'PCB板SN号',
  `timestamp` bigint NOT NULL COMMENT '时间戳（毫秒）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态: 1-已申请未使用, 2-已使用, 3-已废弃',
  `device_type` varchar(32) DEFAULT NULL COMMENT '设备类型',
  `production_line` varchar(32) DEFAULT NULL COMMENT '生产线编号',
  `batch_number` varchar(32) DEFAULT NULL COMMENT '批次号',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作员',
  `applied_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `used_at` timestamp NULL DEFAULT NULL COMMENT '使用时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_sn` (`sn`),
  KEY `idx_pcb_sn` (`pcb_sn`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_status` (`status`),
  KEY `idx_device_type` (`device_type`),
  KEY `idx_production_line` (`production_line`),
  KEY `idx_batch_number` (`batch_number`),
  KEY `idx_applied_at` (`applied_at`),
  KEY `idx_used_at` (`used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SN号记录表';

-- SN号生成序列表（用于确保SN号的唯一性和连续性）
CREATE TABLE `sn_sequence` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `prefix` varchar(4) NOT NULL DEFAULT '' COMMENT 'SN号前缀',
  `current_number` bigint NOT NULL DEFAULT '0' COMMENT '当前序列号',
  `max_number` bigint NOT NULL DEFAULT '99999999' COMMENT '最大序列号（8位数字）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_prefix` (`prefix`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SN号序列表';

-- 插入默认的序列记录（无前缀，8位16进制）
INSERT INTO `sn_sequence` (`prefix`, `current_number`, `max_number`) VALUES ('', 268435456, 4294967295);

-- 生产批次表
CREATE TABLE `production_batches` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_number` varchar(32) NOT NULL COMMENT '批次号',
  `device_type` varchar(32) NOT NULL COMMENT '设备类型',
  `production_line` varchar(32) NOT NULL COMMENT '生产线编号',
  `planned_quantity` int NOT NULL DEFAULT '0' COMMENT '计划生产数量',
  `actual_quantity` int NOT NULL DEFAULT '0' COMMENT '实际生产数量',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态: 1-计划中, 2-生产中, 3-已完成, 4-已取消',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作员',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_batch_number` (`batch_number`),
  KEY `idx_device_type` (`device_type`),
  KEY `idx_production_line` (`production_line`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生产批次表';

-- SN号申请日志表
CREATE TABLE `sn_apply_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sn` varchar(64) NOT NULL COMMENT '复合SN号',
  `action` varchar(32) NOT NULL COMMENT '操作类型: apply-申请, use-使用, discard-废弃',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作员',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  `batch_number` varchar(32) DEFAULT NULL COMMENT '批次号',
  `device_type` varchar(32) DEFAULT NULL COMMENT '设备类型',
  `production_line` varchar(32) DEFAULT NULL COMMENT '生产线编号',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remark` text COMMENT '备注信息',
  PRIMARY KEY (`id`),
  KEY `idx_sn` (`sn`),
  KEY `idx_action` (`action`),
  KEY `idx_operator` (`operator`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_batch_number` (`batch_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SN号申请日志表';
