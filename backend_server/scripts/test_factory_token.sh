#!/bin/bash

# 工厂Token测试脚本
# 使用方法: ./test_factory_token.sh [server_url]

SERVER_URL=${1:-"http://localhost:5678"}
API_BASE="$SERVER_URL/api/factory"

# 工厂永久Token
FACTORY_TOKEN="FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"

echo "测试工厂Token认证API - 服务器: $SERVER_URL"
echo "使用Token: $FACTORY_TOKEN"
echo "=================================================="

# 1. 测试健康检查（无需认证）
echo "1. 测试健康检查（无需认证）..."
curl -s -X GET "$API_BASE/health" | jq '.'
echo ""

# 2. 测试无Token访问（应该失败）
echo "2. 测试无Token访问SN申请（应该失败）..."
curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -d '{
    "device_type": "toilet_v1",
    "quantity": 1
  }' | jq '.'
echo ""

# 3. 测试错误Token（应该失败）
echo "3. 测试错误Token（应该失败）..."
curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer INVALID_TOKEN" \
  -d '{
    "device_type": "toilet_v1",
    "quantity": 1
  }' | jq '.'
echo ""

# 4. 测试正确Token申请SN号
echo "4. 测试正确Token申请SN号..."
APPLY_RESPONSE=$(curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d '{
    "device_type": "toilet_v1",
    "production_line": "line_001", 
    "batch_number": "batch_token_test_001",
    "operator": "Token测试员",
    "quantity": 3,
    "remark": "Token认证测试批次"
  }')

echo "$APPLY_RESPONSE" | jq '.'

# 提取第一个SN号用于后续测试
FIRST_SN=$(echo "$APPLY_RESPONSE" | jq -r '.data.sn_list[0]')
echo "提取的SN号: $FIRST_SN"
echo ""

# 5. 测试Token检查SN是否存在
echo "5. 测试Token检查SN是否存在..."
curl -s -X GET "$API_BASE/sn/$FIRST_SN/check" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 6. 测试Token获取SN详细信息
echo "6. 测试Token获取SN详细信息..."
curl -s -X GET "$API_BASE/sn/$FIRST_SN" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 7. 测试Token更新SN状态
echo "7. 测试Token更新SN状态为已使用..."
curl -s -X PUT "$API_BASE/sn/update" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"sn\": \"$FIRST_SN\",
    \"status\": 2,
    \"operator\": \"Token测试员\",
    \"remark\": \"Token测试使用\"
  }" | jq '.'
echo ""

# 8. 测试Token查询SN号
echo "8. 测试Token查询SN号..."
curl -s -X GET "$API_BASE/sn/query?batch_number=batch_token_test_001&page=1&page_size=10" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 9. 测试Token获取统计信息
echo "9. 测试Token获取统计信息..."
curl -s -X GET "$API_BASE/statistics" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 10. 测试Token格式错误（应该失败）
echo "10. 测试Token格式错误（应该失败）..."
curl -s -X GET "$API_BASE/statistics" \
  -H "Authorization: $FACTORY_TOKEN" | jq '.'
echo ""

echo "=================================================="
echo "Token测试完成!"
echo ""
echo "🔑 您的永久工厂Token:"
echo "FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"
echo ""
echo "📝 使用方法:"
echo "在HTTP请求头中添加:"
echo "Authorization: Bearer FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"
echo ""
echo "🚀 可用的API端点:"
echo "POST   /api/factory/sn/apply     - 申请SN号"
echo "GET    /api/factory/sn/{sn}      - 获取SN详细信息"
echo "GET    /api/factory/sn/{sn}/check - 检查SN是否存在"
echo "PUT    /api/factory/sn/update    - 更新SN状态"
echo "GET    /api/factory/sn/query     - 查询SN号"
echo "GET    /api/factory/statistics   - 获取统计信息"
