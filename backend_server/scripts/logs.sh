#!/bin/bash

# Script to view logs from Docker containers

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Directory where docker-compose.yml is located
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR" || { echo -e "${RED}Failed to change directory to $PROJECT_DIR${NC}"; exit 1; }

# Default values
SERVICE=""
LINES=100
FOLLOW=true

# Function to display usage information
show_usage() {
    echo -e "${GREEN}Docker Logs Viewer${NC}"
    echo "Usage: $0 [options] [service]"
    echo
    echo "Options:"
    echo "  -n, --lines NUMBER   Number of lines to show (default: 100)"
    echo "  -no-follow           Don't follow logs (static output)"
    echo "  -h, --help           Show this help message"
    echo
    echo "Services:"
    echo "  backend              View logs for backend-server"
    echo "  mysql                View logs for MySQL database"
    echo "  minio                View logs for MinIO object storage"
    echo "  all                  View logs for all services (default)"
    echo
    echo "Examples:"
    echo "  $0                   # View logs from all services"
    echo "  $0 backend           # View logs from backend-server only"
    echo "  $0 -n 50 mysql       # View last 50 lines from MySQL"
    echo "  $0 --no-follow minio # View logs from MinIO without following"
}

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        -n|--lines)
            LINES="$2"
            shift 2
            ;;
        --no-follow)
            FOLLOW=false
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        backend|mysql|minio|all)
            SERVICE="$1"
            shift
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            show_usage
            exit 1
            ;;
    esac
done

# Map service names to docker-compose service names
get_docker_service() {
    case $1 in
        backend) echo "backend-server" ;;
        mysql) echo "mysql" ;;
        minio) echo "minio" ;;
        *) echo "$1" ;;
    esac
}

# Function to display logs for a specific service
show_logs() {
    local service=$(get_docker_service "$1")
    local container_id=$(docker-compose ps -q "$service" 2>/dev/null)
    
    if [ -z "$container_id" ]; then
        echo -e "${RED}Error: Service '$1' is not running or doesn't exist.${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}Showing logs for $1 (${BLUE}$container_id${YELLOW})${NC}"
    
    if [ "$FOLLOW" = true ]; then
        docker logs -f --tail "$LINES" "$container_id"
    else
        docker logs --tail "$LINES" "$container_id"
    fi
}

# Main logic
if [ -z "$SERVICE" ] || [ "$SERVICE" = "all" ]; then
    # No specific service specified, show logs for all
    if [ "$FOLLOW" = true ]; then
        echo -e "${YELLOW}Showing logs for all services (last $LINES lines, follow mode)${NC}"
        docker-compose logs -f --tail "$LINES"
    else
        echo -e "${YELLOW}Showing logs for all services (last $LINES lines)${NC}"
        docker-compose logs --tail "$LINES"
    fi
else
    show_logs "$SERVICE"
fi
