#!/bin/bash

# 工厂路由完整测试脚本
# 测试所有工厂相关的API端点
# 使用方法: ./test_factory_routes.sh [server_url]

SERVER_URL=${1:-"http://localhost:5678"}
API_BASE="$SERVER_URL/api/factory"

# 工厂永久Token
FACTORY_TOKEN="FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"

echo "🏭 工厂路由完整测试 - 服务器: $SERVER_URL"
echo "测试所有工厂API端点功能"
echo "=================================================="

# 测试数据
PCB_SN_1="PCB_TEST_ROUTE_001"
PCB_SN_2="PCB_TEST_ROUTE_002"
PCB_SN_3="PCB_BATCH_TEST_001"

# 1. 测试健康检查（公开路由，无需Token）
echo "1. 🔍 测试工厂健康检查（公开路由）..."
curl -s -X GET "$API_BASE/health" | jq '.'
echo ""

# 2. 测试无Token访问（应该失败）
echo "2. ❌ 测试无Token访问SN申请（应该失败）..."
curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -d "{
    \"pcb_sn\": \"$PCB_SN_1\",
    \"device_type\": \"toilet_v1\",
    \"quantity\": 1
  }" | jq '.'
echo ""

# 3. 测试错误Token（应该失败）
echo "3. ❌ 测试错误Token（应该失败）..."
curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer INVALID_TOKEN_123" \
  -d "{
    \"pcb_sn\": \"$PCB_SN_1\",
    \"device_type\": \"toilet_v1\",
    \"quantity\": 1
  }" | jq '.'
echo ""

# 4. 测试正确Token申请单个SN号
echo "4. ✅ 测试申请单个SN号..."
APPLY_RESPONSE_1=$(curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"pcb_sn\": \"$PCB_SN_1\",
    \"device_type\": \"toilet_v1\",
    \"production_line\": \"line_001\", 
    \"batch_number\": \"batch_route_test_001\",
    \"operator\": \"路由测试员\",
    \"quantity\": 1,
    \"remark\": \"单个SN号申请测试\"
  }")

echo "$APPLY_RESPONSE_1" | jq '.'
FIRST_SN=$(echo "$APPLY_RESPONSE_1" | jq -r '.data.sn_list[0]')
echo "生成的SN号: $FIRST_SN"
echo ""

# 5. 测试申请多个SN号
echo "5. ✅ 测试申请多个SN号..."
APPLY_RESPONSE_2=$(curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"pcb_sn\": \"$PCB_SN_2\",
    \"device_type\": \"toilet_v2\",
    \"production_line\": \"line_002\", 
    \"batch_number\": \"batch_route_test_002\",
    \"operator\": \"路由测试员\",
    \"quantity\": 3,
    \"remark\": \"多个SN号申请测试\"
  }")

echo "$APPLY_RESPONSE_2" | jq '.'
SECOND_SN=$(echo "$APPLY_RESPONSE_2" | jq -r '.data.sn_list[0]')
echo "生成的第一个SN号: $SECOND_SN"
echo ""

# 6. 测试检查SN是否存在
echo "6. ✅ 测试检查SN是否存在..."
curl -s -X GET "$API_BASE/sn/$FIRST_SN/check" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 7. 测试获取SN详细信息
echo "7. ✅ 测试获取SN详细信息..."
curl -s -X GET "$API_BASE/sn/$FIRST_SN" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 8. 测试更新SN状态为已使用
echo "8. ✅ 测试更新SN状态为已使用..."
curl -s -X PUT "$API_BASE/sn/update" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"sn\": \"$FIRST_SN\",
    \"status\": 2,
    \"operator\": \"路由测试员\",
    \"remark\": \"路由测试使用\"
  }" | jq '.'
echo ""

# 9. 测试更新SN状态为已废弃
echo "9. ✅ 测试更新SN状态为已废弃..."
curl -s -X PUT "$API_BASE/sn/update" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"sn\": \"$SECOND_SN\",
    \"status\": 3,
    \"operator\": \"路由测试员\",
    \"remark\": \"路由测试废弃\"
  }" | jq '.'
echo ""

# 10. 测试按SN号查询
echo "10. ✅ 测试按SN号查询..."
curl -s -X GET "$API_BASE/sn/query?sn=$FIRST_SN&page=1&page_size=5" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 11. 测试按PCB SN查询
echo "11. ✅ 测试按PCB SN查询..."
curl -s -X GET "$API_BASE/sn/query?pcb_sn=$PCB_SN_1&page=1&page_size=10" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 12. 测试按批次号查询
echo "12. ✅ 测试按批次号查询..."
curl -s -X GET "$API_BASE/sn/query?batch_number=batch_route_test_001&page=1&page_size=10" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 13. 测试按状态查询
echo "13. ✅ 测试按状态查询（已使用）..."
curl -s -X GET "$API_BASE/sn/query?status=2&page=1&page_size=5" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 14. 测试按设备类型查询
echo "14. ✅ 测试按设备类型查询..."
curl -s -X GET "$API_BASE/sn/query?device_type=toilet_v1&page=1&page_size=5" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 15. 测试获取统计信息
echo "15. ✅ 测试获取统计信息..."
curl -s -X GET "$API_BASE/statistics" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 16. 测试错误情况 - 缺少PCB SN
echo "16. ❌ 测试错误情况 - 缺少PCB SN（应该失败）..."
curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d '{
    "device_type": "toilet_v1",
    "quantity": 1
  }' | jq '.'
echo ""

# 17. 测试错误情况 - 空PCB SN
echo "17. ❌ 测试错误情况 - 空PCB SN（应该失败）..."
curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d '{
    "pcb_sn": "",
    "device_type": "toilet_v1",
    "quantity": 1
  }' | jq '.'
echo ""

# 18. 测试错误情况 - 数量超限
echo "18. ❌ 测试错误情况 - 数量超限（应该失败）..."
curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"pcb_sn\": \"$PCB_SN_3\",
    \"device_type\": \"toilet_v1\",
    \"quantity\": 101
  }" | jq '.'
echo ""

# 19. 测试错误情况 - 无效SN状态更新
echo "19. ❌ 测试错误情况 - 无效SN状态更新（应该失败）..."
curl -s -X PUT "$API_BASE/sn/update" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"sn\": \"$FIRST_SN\",
    \"status\": 1,
    \"operator\": \"路由测试员\"
  }" | jq '.'
echo ""

# 20. 测试错误情况 - 不存在的SN号
echo "20. ❌ 测试错误情况 - 不存在的SN号（应该失败）..."
curl -s -X GET "$API_BASE/sn/NONEXISTENT_SN_12345678/check" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

echo "=================================================="
echo "🎉 工厂路由测试完成!"
echo ""
echo "📊 测试总结:"
echo "✅ 成功测试项目:"
echo "  - 健康检查（公开路由）"
echo "  - Token认证机制"
echo "  - SN号申请（单个和批量）"
echo "  - SN号查询和检查"
echo "  - SN号状态更新"
echo "  - 多种查询条件（SN、PCB SN、批次、状态、设备类型）"
echo "  - 统计信息获取"
echo ""
echo "❌ 错误处理验证:"
echo "  - 无Token访问拒绝"
echo "  - 错误Token拒绝"
echo "  - 参数验证（缺少PCB SN、空值、超限等）"
echo "  - 无效操作拒绝"
echo ""
echo "🔑 使用的Token:"
echo "FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"
echo ""
echo "📝 生成的测试SN号:"
echo "第一个: $FIRST_SN"
echo "第二个: $SECOND_SN"
