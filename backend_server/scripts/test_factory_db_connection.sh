#!/bin/bash

# 测试工厂数据库连接脚本

echo "测试工厂数据库连接..."
echo "=========================="

# 从.env文件读取配置
source .env

# 测试连接
echo "1. 测试工厂数据库连接..."
mysql -h 144.126.146.223 -u "$FACTORY_USER" -p"$FACTORY_PASSWORD" "$FACTORY_DATABASE" -e "SELECT 'Factory DB connection successful!' as status;"

if [ $? -eq 0 ]; then
    echo "✅ 工厂数据库连接成功!"
else
    echo "❌ 工厂数据库连接失败!"
    exit 1
fi

echo ""
echo "2. 检查工厂数据库表结构..."
mysql -h 144.126.146.223 -u "$FACTORY_USER" -p"$FACTORY_PASSWORD" "$FACTORY_DATABASE" -e "SHOW TABLES;"

echo ""
echo "3. 检查SN序列表初始数据..."
mysql -h 144.126.146.223 -u "$FACTORY_USER" -p"$FACTORY_PASSWORD" "$FACTORY_DATABASE" -e "SELECT * FROM sn_sequence;"

echo ""
echo "4. 测试SN号生成（插入测试数据）..."
mysql -h 144.126.146.223 -u "$FACTORY_USER" -p"$FACTORY_PASSWORD" "$FACTORY_DATABASE" -e "
INSERT INTO sn_records (sn, device_type, production_line, batch_number, operator, remark) 
VALUES ('100000000001', 'test_device', 'test_line', 'test_batch', 'test_operator', 'Connection test');
"

if [ $? -eq 0 ]; then
    echo "✅ 测试数据插入成功!"
    
    echo ""
    echo "5. 查询测试数据..."
    mysql -h 144.126.146.223 -u "$FACTORY_USER" -p"$FACTORY_PASSWORD" "$FACTORY_DATABASE" -e "
    SELECT * FROM sn_records WHERE sn = '100000000001';
    "
    
    echo ""
    echo "6. 清理测试数据..."
    mysql -h 144.126.146.223 -u "$FACTORY_USER" -p"$FACTORY_PASSWORD" "$FACTORY_DATABASE" -e "
    DELETE FROM sn_records WHERE sn = '100000000001';
    "
    echo "✅ 测试数据清理完成!"
else
    echo "❌ 测试数据插入失败!"
fi

echo ""
echo "工厂数据库配置测试完成!"
