FROM ubuntu:22.04

ARG MINIO_VERSION

ENV DEBIAN_FRONTEND=noninteractive

# Install necessary packages
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install MinIO using deb package
RUN wget https://dl.min.io/server/minio/release/linux-amd64/archive/minio_${MINIO_VERSION}_amd64.deb -O /tmp/minio.deb && \
    dpkg -i /tmp/minio.deb && \
    rm /tmp/minio.deb

# Create directory for MinIO data
RUN mkdir -p /data/minio

# Expose MinIO ports
EXPOSE 9000 9001

# Set volume mount point
VOLUME ["/data/minio"]

CMD ["minio", "server", "/data/minio", "--console-address", ":9001"]
