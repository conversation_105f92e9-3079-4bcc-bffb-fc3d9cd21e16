# 🏠 Backend Server - 智能猫厕所后端服务

智能猫厕所系统的核心后端服务，提供用户管理、设备管理、视频记录处理和AI分析集成功能。

## 📋 项目概述

Backend Server是智能猫厕所系统的核心后端服务，采用Go语言开发，提供RESTful API接口，支持用户认证、设备管理、视频记录处理、AI分析集成等功能。

### 🎯 核心特性

- ✅ **用户管理** - 完整的用户注册、登录、认证体系
- ✅ **设备管理** - 智能猫厕所设备的注册、状态监控、配置管理
- ✅ **视频记录** - 视频文件上传、存储、缩略图生成
- ✅ **AI分析集成** - 与Caby AI服务集成，提供智能视频分析
- ✅ **猫咪管理** - 猫咪档案管理、个体识别结果存储
- ✅ **家庭群组** - 多用户家庭共享功能
- ✅ **通知系统** - 实时消息推送和通知管理
- ✅ **数据存储** - MySQL数据库 + MinIO对象存储
- ✅ **容器化部署** - Docker Compose一键部署

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │───▶│ Backend Server  │───▶│   Caby AI       │
│   (客户端)      │    │  (端口: 5678)   │    │  (端口: 8765)   │
│                 │    │                 │    │                 │
│ • 用户界面      │    │ • API网关       │    │ • 视频分析      │
│ • 设备控制      │    │ • 用户认证      │    │ • 猫咪识别      │
│ • 数据展示      │    │ • 数据管理      │    │ • 行为分析      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Smart Device   │    │   MySQL 数据库  │    │  Caby Vision    │
│  (智能猫厕所)    │    │  (结构化数据)   │    │  (端口: 8001)   │
│                 │    │                 │    │                 │
│ • 传感器数据    │    │ • 用户信息      │    │ • 深度学习模型  │
│ • 视频录制      │    │ • 设备状态      │    │ • 猫咪识别      │
│ • 重量检测      │    │ • 记录数据      │    │ • 特征提取      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  MinIO 存储     │
                       │  (对象存储)     │
                       │                 │
                       │ • 视频文件      │
                       │ • 缩略图        │
                       │ • 静态资源      │
                       └─────────────────┘
```

### 🔄 AI分析工作流程

1. **设备上传** - 智能猫厕所设备上传视频记录
2. **记录存储** - Backend Server存储视频文件和元数据
3. **AI分析请求** - 客户端或自动触发AI分析
4. **Caby AI处理** - 调用Caby AI进行视频分析和猫咪识别
5. **结果存储** - 分析结果存储到数据库
6. **通知推送** - 向用户推送分析结果

## 🗂️ 目录结构

```
backend_server/
├── api/                        # API路由层
│   ├── routers.go             # 主路由配置
│   ├── router_users.go        # 用户管理API
│   ├── router_devices.go      # 设备管理API
│   ├── router_records.go      # 视频记录API
│   ├── router_cats.go         # 猫咪管理API
│   ├── router_family-groups.go # 家庭群组API
│   └── router_notifications.go # 通知API
├── pkg/                       # 核心业务逻辑
│   ├── cattoilet/            # 猫厕所业务逻辑
│   ├── auth/                 # 认证服务
│   ├── storage/              # 存储服务
│   ├── notification/         # 通知服务
│   └── utils/                # 工具函数
├── config/                   # 配置文件
│   ├── config.yaml          # 主配置文件
│   └── config.yaml.template # 配置模板
├── database/                # 数据库相关
│   └── mysql.sql           # 数据库初始化脚本
├── scripts/                 # 工具脚本
│   ├── quick_deploy.sh     # 快速部署脚本
│   ├── logs.sh             # 日志查看脚本
│   ├── test.py             # API测试脚本
│   └── init-db.sh          # 数据库初始化脚本
├── doc/                     # 文档
│   └── backend_api.md      # API文档
├── docker-compose.yml      # Docker编排文件
├── Dockerfile.backend      # 后端服务容器构建文件
├── Dockerfile.mysql        # MySQL容器构建文件
├── Dockerfile.minio        # MinIO容器构建文件
└── README.md               # 项目文档
```

## 🚀 快速开始

### 先决条件

- Docker 20.10+
- Docker Compose 2.0+
- 至少4GB可用内存
- 至少10GB可用磁盘空间

### 一键部署

```bash
# 克隆项目
git clone <repository-url>
cd backend_server

# 配置环境变量
cp config/config.yaml.template config/config.yaml
# 编辑 config/config.yaml 文件设置你的配置

# 一键部署
./scripts/quick_deploy.sh
```

### 手动部署

```bash
# 构建并启动服务
docker-compose up -d

# 初始化数据库
./scripts/init-db.sh

# 查看服务状态
docker-compose ps

# 查看日志
./scripts/logs.sh
```

## 🔧 配置说明

### 主要配置项

```yaml
server:
  listen_addr: ":5678"
  cors_origins: ["*"]

database:
  host: "mysql"
  port: 3306
  username: "root"
  password: "your_password"
  database: "cabycare"

storage:
  minio:
    endpoint: "minio:9000"
    access_key: "your_access_key"
    secret_key: "your_secret_key"
    bucket: "cabycare"

ai_service:
  caby_ai_url: "http://caby_ai:8765"
  service_token: "your_service_token"

auth:
  logto:
    endpoint: "https://your-logto-instance.com"
    app_id: "your_app_id"
    app_secret: "your_app_secret"
```

## 📡 API接口

### 核心API端点

#### 用户管理
- `POST /api/register` - 用户注册
- `POST /api/login` - 用户登录
- `GET /api/users/profile` - 获取用户信息
- `PUT /api/users/profile` - 更新用户信息

#### 设备管理
- `POST /api/devices` - 注册设备
- `GET /api/devices` - 获取设备列表
- `GET /api/devices/{id}` - 获取设备详情
- `PUT /api/devices/{id}/settings` - 更新设备设置
- `POST /api/devices/{id}/heartbeat` - 设备心跳

#### 视频记录
- `POST /api/records/videos` - 上传视频记录
- `GET /api/records/videos` - 获取视频记录列表
- `GET /api/records/videos/{id}` - 获取视频记录详情
- `GET /api/records/videos/thumbnail/{folder}` - 获取缩略图
- `POST /api/records/videos/{id}/analyze` - 触发AI分析

#### 猫咪管理
- `POST /api/cats` - 添加猫咪
- `GET /api/cats` - 获取猫咪列表
- `GET /api/cats/{id}` - 获取猫咪详情
- `PUT /api/cats/{id}` - 更新猫咪信息

### AI分析集成

#### 视频分析请求
```bash
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001"
  }' \
  http://localhost:5678/api/records/videos/VIDEO_ID/analyze
```

#### 分析结果响应
```json
{
  "is_static": false,
  "analysis": {
    "video_id": "video_123456",
    "animal_id": "f3ce1b02b2c1d755421000",
    "cat_confidence": 0.8756,
    "behavior_type": "normal_poop",
    "is_abnormal": false,
    "abnormal_type": "",
    "abnormal_prob": 0.05,
    "created_at": "2025-01-01T10:00:00Z",
    "updated_at": "2025-01-01T10:00:00Z"
  }
}
```

## 🧪 测试

### 运行测试脚本

```bash
# 运行所有测试
python scripts/test.py

# 运行特定测试
python scripts/test.py health
python scripts/test.py auth
python scripts/test.py devices
python scripts/test.py records
python scripts/test.py ai_analysis

# 使用自定义配置
python scripts/test.py --url http://localhost:5678 --token YOUR_TOKEN
```

### 测试覆盖范围

- ✅ 健康检查
- ✅ 用户认证
- ✅ 设备管理
- ✅ 视频记录上传
- ✅ AI分析集成
- ✅ 猫咪管理
- ✅ 通知系统

## 🔍 监控和日志

### 查看日志

```bash
# 查看所有服务日志
./scripts/logs.sh

# 查看特定服务日志
docker-compose logs backend_server
docker-compose logs mysql
docker-compose logs minio

# 实时跟踪日志
docker-compose logs -f backend_server
```

### 健康检查

```bash
# 检查服务状态
curl http://localhost:5678/health

# 检查数据库连接
curl http://localhost:5678/api/health/database

# 检查存储服务
curl http://localhost:5678/api/health/storage
```

## 🔧 维护和运维

### 数据备份

```bash
# 备份MySQL数据库
docker-compose exec mysql mysqldump -u root -p cabycare > backup.sql

# 备份MinIO数据
docker-compose exec minio mc mirror /data/cabycare ./backup/
```

### 服务重启

```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend_server
docker-compose restart mysql
docker-compose restart minio
```

### 扩容和优化

```bash
# 查看资源使用情况
docker stats

# 调整服务配置
# 编辑 docker-compose.yml 中的资源限制
# 重新部署服务
docker-compose up -d
```

## 🤝 开发指南

### 本地开发环境

```bash
# 安装Go依赖
go mod download

# 运行开发服务器
go run main.go

# 运行测试
go test ./...

# 代码格式化
go fmt ./...
```

### API开发

1. 在 `api/` 目录下添加新的路由文件
2. 在 `pkg/` 目录下实现业务逻辑
3. 更新 `doc/backend_api.md` 文档
4. 添加相应的测试用例

### 数据库迁移

```bash
# 创建新的迁移文件
# 编辑 database/mysql.sql

# 应用迁移
./scripts/init-db.sh
```

## 📚 相关文档

- [API详细文档](doc/backend_api.md)
- [Caby AI集成指南](../caby_ai/README.md)
- [Caby Vision模型文档](../caby_vision/README.md)
- [部署运维指南](scripts/README.md)

## 🆘 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口占用：`netstat -tulpn | grep 5678`
   - 检查Docker服务：`docker-compose ps`
   - 查看错误日志：`docker-compose logs backend_server`

2. **数据库连接失败**
   - 检查MySQL服务状态：`docker-compose ps mysql`
   - 验证数据库配置：检查 `config/config.yaml`
   - 重新初始化数据库：`./scripts/init-db.sh`

3. **AI分析失败**
   - 检查Caby AI服务状态：`curl http://localhost:8765/health`
   - 验证服务令牌配置
   - 查看AI服务日志：`docker-compose logs caby_ai`

4. **存储服务问题**
   - 检查MinIO服务：`docker-compose ps minio`
   - 验证存储配置和权限
   - 检查磁盘空间：`df -h`

### 获取帮助

- 查看项目文档：[README.md](README.md)
- 提交Issue：[GitHub Issues](https://github.com/your-repo/issues)
- 联系开发团队：[<EMAIL>](mailto:<EMAIL>)

---

**Backend Server** - 为智能猫厕所系统提供稳定可靠的后端服务支持 🐱
